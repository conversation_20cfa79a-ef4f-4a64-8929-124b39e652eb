﻿<Project Sdk="Microsoft.NET.Sdk">

    <!--using Directory.Build.props-->

    <PropertyGroup>
        <Title>Camera addon to DrawnUI for .NET MAUI</Title>
        <PackageId>DrawnUi.Maui.Camera</PackageId>
        <Description>Camera implementation for Android, iOS, and Windows with preview rendering using SkiaSharp in .NET MAUI</Description>
        <PackageTags>maui drawnui skia skiasharp draw camera</PackageTags>
        <Packable>true</Packable>
        <SymbolPackageFormat>snupkg</SymbolPackageFormat>
        <CreatePackage>false</CreatePackage>
        <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    </PropertyGroup>

    <PropertyGroup Condition="$(TargetFramework.Contains('windows')) == true Or $(TargetFramework.Contains('droid')) == true Or $(TargetFramework.Contains('ios')) == true Or $(TargetFramework.Contains('catalyst')) == true">
        <DefineConstants>$(DefineConstants);ONPLATFORM</DefineConstants>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)'=='Release'">
        <WarningsAsErrors>$(WarningsAsErrors);CS0108</WarningsAsErrors>
    </PropertyGroup>

    <ItemGroup>
        <Compile Remove="Shared/**/*.*pple.cs" />
        <None Include="Shared/**/*.*pple.cs" />
    </ItemGroup>

    <ItemGroup Condition="$(TargetFramework.Contains('catalyst'))">
        <Compile Include="Shared/**/*.*pple.cs">
            <Link>Platforms/MacCatalyst/%(RecursiveDir)%(Filename)%(Extension)</Link>
        </Compile>
    </ItemGroup>

    <ItemGroup Condition="$(TargetFramework.Contains('ios'))">
        <Compile Include="Shared/**/*.*pple.cs">
            <Link>Platforms/iOS/%(RecursiveDir)%(Filename)%(Extension)</Link>
        </Compile>
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Platforms\iOS\" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\DrawnUi\DrawnUi.Maui.csproj" />
    </ItemGroup>


</Project>