<?xml version="1.0" encoding="UTF-8" ?>
<Shell
    x:Class="ShadersCamera.AppShell"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:views1="clr-namespace:AppoMobi.Maui.DrawnUi.Demo.Views"
    Title="DrawnUI for .NET MAUI"
    Shell.FlyoutBehavior="Disabled">

    <ShellContent
        Title="Drawn UI for .NET MAUI"
        ContentTemplate="{DataTemplate views1:MainPageShadersCamera}"
        Route="MainPage" />

</Shell>
