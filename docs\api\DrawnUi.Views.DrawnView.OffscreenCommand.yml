### YamlMime:ManagedReference
items:
- uid: DrawnUi.Views.DrawnView.OffscreenCommand
  commentId: T:DrawnUi.Views.DrawnView.OffscreenCommand
  id: DrawnView.OffscreenCommand
  parent: DrawnUi.Views
  children:
  - DrawnUi.Views.DrawnView.OffscreenCommand.#ctor(DrawnUi.Draw.SkiaControl,System.Threading.CancellationToken)
  - DrawnUi.Views.DrawnView.OffscreenCommand.Cancel
  - DrawnUi.Views.DrawnView.OffscreenCommand.Control
  langs:
  - csharp
  - vb
  name: DrawnView.OffscreenCommand
  nameWithType: DrawnView.OffscreenCommand
  fullName: DrawnUi.Views.DrawnView.OffscreenCommand
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Views/DrawnView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OffscreenCommand
    path: ../src/Maui/DrawnUi/Views/DrawnView.cs
    startLine: 1820
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: 'public record DrawnView.OffscreenCommand : IEquatable<DrawnView.OffscreenCommand>'
    content.vb: Public Class DrawnView.OffscreenCommand Implements IEquatable(Of DrawnView.OffscreenCommand)
  inheritance:
  - System.Object
  implements:
  - System.IEquatable{DrawnUi.Views.DrawnView.OffscreenCommand}
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Views.DrawnView.OffscreenCommand.#ctor(DrawnUi.Draw.SkiaControl,System.Threading.CancellationToken)
  commentId: M:DrawnUi.Views.DrawnView.OffscreenCommand.#ctor(DrawnUi.Draw.SkiaControl,System.Threading.CancellationToken)
  id: '#ctor(DrawnUi.Draw.SkiaControl,System.Threading.CancellationToken)'
  parent: DrawnUi.Views.DrawnView.OffscreenCommand
  langs:
  - csharp
  - vb
  name: OffscreenCommand(SkiaControl, CancellationToken)
  nameWithType: DrawnView.OffscreenCommand.OffscreenCommand(SkiaControl, CancellationToken)
  fullName: DrawnUi.Views.DrawnView.OffscreenCommand.OffscreenCommand(DrawnUi.Draw.SkiaControl, System.Threading.CancellationToken)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Views/DrawnView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Views/DrawnView.cs
    startLine: 1820
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public OffscreenCommand(SkiaControl Control, CancellationToken Cancel)
    parameters:
    - id: Control
      type: DrawnUi.Draw.SkiaControl
    - id: Cancel
      type: System.Threading.CancellationToken
    content.vb: Public Sub New(Control As SkiaControl, Cancel As CancellationToken)
  overload: DrawnUi.Views.DrawnView.OffscreenCommand.#ctor*
  nameWithType.vb: DrawnView.OffscreenCommand.New(SkiaControl, CancellationToken)
  fullName.vb: DrawnUi.Views.DrawnView.OffscreenCommand.New(DrawnUi.Draw.SkiaControl, System.Threading.CancellationToken)
  name.vb: New(SkiaControl, CancellationToken)
- uid: DrawnUi.Views.DrawnView.OffscreenCommand.Control
  commentId: P:DrawnUi.Views.DrawnView.OffscreenCommand.Control
  id: Control
  parent: DrawnUi.Views.DrawnView.OffscreenCommand
  langs:
  - csharp
  - vb
  name: Control
  nameWithType: DrawnView.OffscreenCommand.Control
  fullName: DrawnUi.Views.DrawnView.OffscreenCommand.Control
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/DrawnView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Control
    path: ../src/Maui/DrawnUi/Views/DrawnView.cs
    startLine: 1820
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public SkiaControl Control { get; init; }
    parameters: []
    return:
      type: DrawnUi.Draw.SkiaControl
    content.vb: Public Property Control As SkiaControl
  overload: DrawnUi.Views.DrawnView.OffscreenCommand.Control*
- uid: DrawnUi.Views.DrawnView.OffscreenCommand.Cancel
  commentId: P:DrawnUi.Views.DrawnView.OffscreenCommand.Cancel
  id: Cancel
  parent: DrawnUi.Views.DrawnView.OffscreenCommand
  langs:
  - csharp
  - vb
  name: Cancel
  nameWithType: DrawnView.OffscreenCommand.Cancel
  fullName: DrawnUi.Views.DrawnView.OffscreenCommand.Cancel
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/DrawnView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Cancel
    path: ../src/Maui/DrawnUi/Views/DrawnView.cs
    startLine: 1820
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public CancellationToken Cancel { get; init; }
    parameters: []
    return:
      type: System.Threading.CancellationToken
    content.vb: Public Property Cancel As CancellationToken
  overload: DrawnUi.Views.DrawnView.OffscreenCommand.Cancel*
references:
- uid: DrawnUi.Views
  commentId: N:DrawnUi.Views
  href: DrawnUi.html
  name: DrawnUi.Views
  nameWithType: DrawnUi.Views
  fullName: DrawnUi.Views
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Views
    name: Views
    href: DrawnUi.Views.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Views
    name: Views
    href: DrawnUi.Views.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.IEquatable{DrawnUi.Views.DrawnView.OffscreenCommand}
  commentId: T:System.IEquatable{DrawnUi.Views.DrawnView.OffscreenCommand}
  parent: System
  definition: System.IEquatable`1
  href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  name: IEquatable<DrawnView.OffscreenCommand>
  nameWithType: IEquatable<DrawnView.OffscreenCommand>
  fullName: System.IEquatable<DrawnUi.Views.DrawnView.OffscreenCommand>
  nameWithType.vb: IEquatable(Of DrawnView.OffscreenCommand)
  fullName.vb: System.IEquatable(Of DrawnUi.Views.DrawnView.OffscreenCommand)
  name.vb: IEquatable(Of DrawnView.OffscreenCommand)
  spec.csharp:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: <
  - uid: DrawnUi.Views.DrawnView
    name: DrawnView
    href: DrawnUi.Views.DrawnView.html
  - name: .
  - uid: DrawnUi.Views.DrawnView.OffscreenCommand
    name: OffscreenCommand
    href: DrawnUi.Views.DrawnView.OffscreenCommand.html
  - name: '>'
  spec.vb:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Views.DrawnView
    name: DrawnView
    href: DrawnUi.Views.DrawnView.html
  - name: .
  - uid: DrawnUi.Views.DrawnView.OffscreenCommand
    name: OffscreenCommand
    href: DrawnUi.Views.DrawnView.OffscreenCommand.html
  - name: )
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: System.IEquatable`1
  commentId: T:System.IEquatable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  name: IEquatable<T>
  nameWithType: IEquatable<T>
  fullName: System.IEquatable<T>
  nameWithType.vb: IEquatable(Of T)
  fullName.vb: System.IEquatable(Of T)
  name.vb: IEquatable(Of T)
  spec.csharp:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Views.DrawnView.OffscreenCommand.#ctor*
  commentId: Overload:DrawnUi.Views.DrawnView.OffscreenCommand.#ctor
  href: DrawnUi.Views.DrawnView.OffscreenCommand.html#DrawnUi_Views_DrawnView_OffscreenCommand__ctor_DrawnUi_Draw_SkiaControl_System_Threading_CancellationToken_
  name: OffscreenCommand
  nameWithType: DrawnView.OffscreenCommand.OffscreenCommand
  fullName: DrawnUi.Views.DrawnView.OffscreenCommand.OffscreenCommand
  nameWithType.vb: DrawnView.OffscreenCommand.New
  fullName.vb: DrawnUi.Views.DrawnView.OffscreenCommand.New
  name.vb: New
- uid: DrawnUi.Draw.SkiaControl
  commentId: T:DrawnUi.Draw.SkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl
  nameWithType: SkiaControl
  fullName: DrawnUi.Draw.SkiaControl
- uid: System.Threading.CancellationToken
  commentId: T:System.Threading.CancellationToken
  parent: System.Threading
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.threading.cancellationtoken
  name: CancellationToken
  nameWithType: CancellationToken
  fullName: System.Threading.CancellationToken
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Threading
  commentId: N:System.Threading
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Threading
  nameWithType: System.Threading
  fullName: System.Threading
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Threading
    name: Threading
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Threading
    name: Threading
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading
- uid: DrawnUi.Views.DrawnView.OffscreenCommand.Control*
  commentId: Overload:DrawnUi.Views.DrawnView.OffscreenCommand.Control
  href: DrawnUi.Views.DrawnView.OffscreenCommand.html#DrawnUi_Views_DrawnView_OffscreenCommand_Control
  name: Control
  nameWithType: DrawnView.OffscreenCommand.Control
  fullName: DrawnUi.Views.DrawnView.OffscreenCommand.Control
- uid: DrawnUi.Views.DrawnView.OffscreenCommand.Cancel*
  commentId: Overload:DrawnUi.Views.DrawnView.OffscreenCommand.Cancel
  href: DrawnUi.Views.DrawnView.OffscreenCommand.html#DrawnUi_Views_DrawnView_OffscreenCommand_Cancel
  name: Cancel
  nameWithType: DrawnView.OffscreenCommand.Cancel
  fullName: DrawnUi.Views.DrawnView.OffscreenCommand.Cancel
