﻿using Android.Media;
using SkiaSharp.Views.Android;
using Exception = System.Exception;
using Trace = System.Diagnostics.Trace;

namespace DrawnUi.Camera;

// Lightweight container for raw Android Image - lazy processing like iOS
internal class RawAndroidImageData : IDisposable
{
    public Android.Media.Image AndroidImage { get; set; }
    public DateTime Time { get; set; }
    public CameraPosition Facing { get; set; }
    public int Orientation { get; set; }

    public void Dispose()
    {
        AndroidImage?.Close();
        AndroidImage = null;
    }
}

public partial class NativeCamera : Java.Lang.Object, ImageReader.IOnImageAvailableListener, INativeCamera
{
    /// <summary>
    /// IOnImageAvailableListener - Lazy processing like iOS
    /// </summary>
    /// <param name="reader"></param>
    public void OnImageAvailable(ImageReader reader)
    {
        if (FormsControl == null || CapturingStill || State != CameraProcessorState.Enabled)
            return;

        // THROTTLING: Only skip if previous frame is still being processed (prevents thread overwhelm)
        if (_isProcessingFrame)
        {
            _skippedFrameCount++;
            return;
        }

        _isProcessingFrame = true;
        _processedFrameCount++;

        // Log stats every 300 frames
        if (_processedFrameCount % 300 == 0)
        {
            System.Diagnostics.Debug.WriteLine($"[NativeCameraAndroid] Frame stats - Processed: {_processedFrameCount}, Skipped: {_skippedFrameCount}");
        }

        Android.Media.Image image = null;
        bool hasFrame = false;
        try
        {
            // ImageReader
            image = reader.AcquireLatestImage();
            if (image != null)
            {
                // LAZY PROCESSING: Store raw Android Image without heavy processing
                var rawFrame = new RawAndroidImageData
                {
                    AndroidImage = image,
                    Time = DateTime.UtcNow,
                    Facing = FormsControl.Facing,
                    Orientation = FormsControl.DeviceRotation
                };

                SetRawFrame(rawFrame);
                hasFrame = true;
                image = null; // Don't close it here - stored in rawFrame
            }
        }
        catch (Exception e)
        {
            Trace.WriteLine($"[NativeCameraAndroid] Frame processing error: {e.Message}");
        }
        finally
        {
            if (image != null)
            {
                image.Close();
            }

            if (hasFrame)
            {
                FormsControl.UpdatePreview();
            }

            // IMPORTANT: Always reset processing flag
            _isProcessingFrame = false;
        }
    }

    /// <summary>
    /// Store raw frame data for lazy processing - similar to iOS pattern
    /// </summary>
    void SetRawFrame(RawAndroidImageData rawFrame)
    {
        lock (_lockRawFrame)
        {
            // Dispose old raw frame data immediately to prevent memory accumulation
            _latestRawFrame?.Dispose();
            _latestRawFrame = rawFrame;
        }
    }
}
