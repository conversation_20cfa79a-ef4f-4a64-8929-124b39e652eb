﻿using Android.Media;
using SkiaSharp.Views.Android;
using Exception = System.Exception;
using Trace = System.Diagnostics.Trace;

namespace DrawnUi.Camera;

// Lightweight container for raw YUV frame data - lazy processing like iOS
internal class RawYuvFrameData : IDisposable
{
    public byte[] YPlaneData { get; set; }
    public byte[] UPlaneData { get; set; }
    public byte[] VPlaneData { get; set; }
    public int Width { get; set; }
    public int Height { get; set; }
    public int YRowStride { get; set; }
    public int UvRowStride { get; set; }
    public int UvPixelStride { get; set; }
    public DateTime Time { get; set; }
    public CameraPosition Facing { get; set; }
    public int Orientation { get; set; }

    public void Dispose()
    {
        YPlaneData = null;
        UPlaneData = null;
        VPlaneData = null;
    }
}

public partial class NativeCamera : Java.Lang.Object, ImageReader.IOnImageAvailableListener, INativeCamera
{
    /// <summary>
    /// IOnImageAvailableListener - Lazy processing like iOS
    /// </summary>
    /// <param name="reader"></param>
    public void OnImageAvailable(ImageReader reader)
    {
        if (FormsControl == null || CapturingStill || State != CameraProcessorState.Enabled)
            return;

        // THROTTLING: Only skip if previous frame is still being processed (prevents thread overwhelm)
        if (_isProcessingFrame)
        {
            _skippedFrameCount++;
            return;
        }

        _isProcessingFrame = true;
        _processedFrameCount++;

        // Log stats every 300 frames
        if (_processedFrameCount % 300 == 0)
        {
            System.Diagnostics.Debug.WriteLine($"[NativeCameraAndroid] Frame stats - Processed: {_processedFrameCount}, Skipped: {_skippedFrameCount}");
        }

        Android.Media.Image image = null;
        bool hasFrame = false;
        try
        {
            // ImageReader
            image = reader.AcquireLatestImage();
            if (image != null)
            {
                // LAZY PROCESSING: Extract raw YUV data without heavy processing
                var rawFrame = ExtractRawYuvData(image);
                if (rawFrame != null)
                {
                    SetRawFrame(rawFrame);
                    hasFrame = true;
                }
            }
        }
        catch (Exception e)
        {
            Trace.WriteLine($"[NativeCameraAndroid] Frame processing error: {e.Message}");
        }
        finally
        {
            if (image != null)
            {
                image.Close();
            }

            if (hasFrame)
            {
                FormsControl.UpdatePreview();
            }

            // IMPORTANT: Always reset processing flag
            _isProcessingFrame = false;
        }
    }

    /// <summary>
    /// Extract raw YUV data from Android Image - minimal processing like iOS
    /// </summary>
    private RawYuvFrameData ExtractRawYuvData(Android.Media.Image image)
    {
        try
        {
            var planes = image.GetPlanes();
            if (planes.Length < 3)
                return null;

            var width = image.Width;
            var height = image.Height;

            // Extract Y plane
            var yBuffer = planes[0].Buffer;
            var ySize = yBuffer.Remaining();
            var yData = new byte[ySize];
            yBuffer.Get(yData);
            var yRowStride = planes[0].RowStride;

            // Extract U plane
            var uBuffer = planes[1].Buffer;
            var uSize = uBuffer.Remaining();
            var uData = new byte[uSize];
            uBuffer.Get(uData);
            var uvRowStride = planes[1].RowStride;
            var uvPixelStride = planes[1].PixelStride;

            // Extract V plane
            var vBuffer = planes[2].Buffer;
            var vSize = vBuffer.Remaining();
            var vData = new byte[vSize];
            vBuffer.Get(vData);

            return new RawYuvFrameData
            {
                YPlaneData = yData,
                UPlaneData = uData,
                VPlaneData = vData,
                Width = width,
                Height = height,
                YRowStride = yRowStride,
                UvRowStride = uvRowStride,
                UvPixelStride = uvPixelStride,
                Time = DateTime.UtcNow,
                Facing = FormsControl.Facing,
                Orientation = FormsControl.DeviceRotation
            };
        }
        catch (Exception e)
        {
            Trace.WriteLine($"[NativeCameraAndroid] ExtractRawYuvData error: {e}");
            return null;
        }
    }

    /// <summary>
    /// Store raw frame data for lazy processing - similar to iOS pattern
    /// </summary>
    void SetRawFrame(RawYuvFrameData rawFrame)
    {
        lock (_lockRawFrame)
        {
            // Dispose old raw frame data immediately to prevent memory accumulation
            _latestRawFrame?.Dispose();
            _latestRawFrame = rawFrame;
        }
    }
}
