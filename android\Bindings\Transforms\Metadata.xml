﻿<metadata>
  <!--
  This sample removes the class: android.support.v4.content.AsyncTaskLoader.LoadTask:
  <remove-node path="/api/package[@name='android.support.v4.content']/class[@name='AsyncTaskLoader.LoadTask']" />
  
  This sample removes the method: android.support.v4.content.CursorLoader.loadInBackground:
  <remove-node path="/api/package[@name='android.support.v4.content']/class[@name='CursorLoader']/method[@name='loadInBackground']" />
  -->

  <!-- Change namespace -->

  <attr path="/api/package[@name='com.appomobi.nativetools.graphics']" name="managedName">AppoMobi.Maui.Native.Droid.Graphics</attr>
  <attr path="/api/package[@name='com.appomobi.nativetools.buffers']" name="managedName">AppoMobi.Maui.Native.Droid.Buffers</attr>

  <attr path="/api/package[@name='com.appomobi.nativetools.graphics']/class[@name='ScriptC_Scripts']" name="managedName">RenderScripts</attr>

</metadata>
