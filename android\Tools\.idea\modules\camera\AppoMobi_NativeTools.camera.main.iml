<?xml version="1.0" encoding="UTF-8"?>
<module external.linked.project.id=":camera:main" external.linked.project.path="$MODULE_DIR$/../../../camera" external.root.project.path="$MODULE_DIR$/../../.." external.system.id="GRADLE" external.system.module.type="sourceSet" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="android" name="Android">
      <configuration>
        <option name="SELECTED_BUILD_VARIANT" value="debug" />
        <option name="ASSEMBLE_TASK_NAME" value="assembleDebug" />
        <option name="COMPILE_JAVA_TASK_NAME" value="compileDebugSources" />
        <afterSyncTasks>
          <task>generateDebugSources</task>
        </afterSyncTasks>
        <option name="ALLOW_USER_CONFIGURATION" value="false" />
        <option name="MANIFEST_FILE_RELATIVE_PATH" value="/src/main/AndroidManifest.xml" />
        <option name="RES_FOLDER_RELATIVE_PATH" value="/src/main/res" />
        <option name="RES_FOLDERS_RELATIVE_PATH" value="file://$MODULE_DIR$/../../../camera/src/main/res;file://$MODULE_DIR$/../../../camera/src/debug/res;file://$MODULE_DIR$/../../../camera/build/generated/res/rs/debug" />
        <option name="TEST_RES_FOLDERS_RELATIVE_PATH" value="file://$MODULE_DIR$/../../../camera/src/androidTest/res;file://$MODULE_DIR$/../../../camera/src/androidTestDebug/res;file://$MODULE_DIR$/../../../camera/build/generated/res/rs/androidTest/debug" />
        <option name="ASSETS_FOLDER_RELATIVE_PATH" value="/src/main/assets" />
        <option name="PROJECT_TYPE" value="1" />
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_7" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$/../../../camera/build/generated/aidl_source_output_dir/debug/out" />
    <content url="file://$MODULE_DIR$/../../../camera/build/generated/ap_generated_sources/debug/out">
      <sourceFolder url="file://$MODULE_DIR$/../../../camera/build/generated/ap_generated_sources/debug/out" isTestSource="false" generated="true" />
    </content>
    <content url="file://$MODULE_DIR$/../../../camera/build/generated/renderscript_source_output_dir/debug/out">
      <sourceFolder url="file://$MODULE_DIR$/../../../camera/build/generated/renderscript_source_output_dir/debug/out" isTestSource="false" generated="true" />
    </content>
    <content url="file://$MODULE_DIR$/../../../camera/build/generated/res/rs/debug">
      <sourceFolder url="file://$MODULE_DIR$/../../../camera/build/generated/res/rs/debug" type="java-resource" />
    </content>
    <content url="file://$MODULE_DIR$/../../../camera/build/generated/source/buildConfig/debug">
      <sourceFolder url="file://$MODULE_DIR$/../../../camera/build/generated/source/buildConfig/debug" isTestSource="false" generated="true" />
    </content>
    <content url="file://$MODULE_DIR$/../../../camera/src/debug" />
    <content url="file://$MODULE_DIR$/../../../camera/src/main">
      <sourceFolder url="file://$MODULE_DIR$/../../../camera/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/../../../camera/src/main/rs" isTestSource="false" />
    </content>
    <orderEntry type="jdk" jdkName="Android API 33, extension level 3 Platform" jdkType="Android SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Gradle: androidx.collection:collection:1.1.0" level="project" />
    <orderEntry type="library" name="Gradle: androidx.arch.core:core-common:2.1.0" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-common:2.1.0" level="project" />
    <orderEntry type="library" name="Gradle: androidx.annotation:annotation:1.1.0" level="project" />
    <orderEntry type="library" name="Gradle: androidx.appcompat:appcompat:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.fragment:fragment:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.appcompat:appcompat-resources:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.drawerlayout:drawerlayout:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.viewpager:viewpager:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.loader:loader:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.activity:activity:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.vectordrawable:vectordrawable-animated:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.vectordrawable:vectordrawable:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.customview:customview:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.core:core:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.cursoradapter:cursoradapter:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.versionedparcelable:versionedparcelable:1.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-viewmodel:2.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-runtime:2.1.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.savedstate:savedstate:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.interpolator:interpolator:1.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-livedata:2.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.lifecycle:lifecycle-livedata-core:2.0.0@aar" level="project" />
    <orderEntry type="library" name="Gradle: androidx.arch.core:core-runtime:2.0.0@aar" level="project" />
  </component>
</module>