package com.appomobi.nativetools.graphics;

import android.renderscript.Element;
import android.renderscript.RenderScript;
import android.renderscript.ScriptIntrinsicLUT;

import static java.lang.Math.round;

public class ChannelSplinePreset
{
    public ScriptIntrinsicLUT ScriptIntrinsicLUT;


    public ChannelSplinePreset()
    {
    }

    public String id;

    public ChannelSpline splineR;
    public ChannelSpline splineG;
    public ChannelSpline splineB;

    public ScriptField_Spline.Item cacheR;
    public ScriptField_Spline.Item cacheG;
    public ScriptField_Spline.Item cacheB;

    static float ComputeSplineY (ChannelSpline spline, float x)
    {
        //   if (x==0.0)
        //     return 1.0;

        int length = 3;

        // Find which spline can be used to compute this x (by simultaneous traverse)
        int j = 0;
        while ((j < length - 2) && (x > spline.xs[j + 1]))
        {
            j++;
        }

        // Evaluate using j'th spline
        float dx = spline.xs[j + 1] - spline.xs[j];

        float t = (x - spline.xs[j]) / dx;


        float y = (1 - t) * spline.ys[j] + t * spline.ys[j + 1] + t * (1 - t) * (spline.as[j] * (1 - t) + spline.bs[j] * t); // equation 9

        if (y<0)
            y=0;

        if (y>1)
            y=1;

        return y;
    }

    public void CreateLUT(RenderScript rs)
    {
        ScriptIntrinsicLUT = ScriptIntrinsicLUT.create(rs, Element.U8_4(rs));

        for (int x = 0; x < 256; x++)
        {
            float fX = x / 255f;
            float yR = ComputeSplineY(splineR, fX);
            float yG = ComputeSplineY(splineG, fX);
            float yB = ComputeSplineY(splineB, fX);

            ScriptIntrinsicLUT.setAlpha(x, 255);
            ScriptIntrinsicLUT.setRed(x, (int) round(yR * 255.0));
            ScriptIntrinsicLUT.setGreen(x, (int) round(yG * 255.0));
            ScriptIntrinsicLUT.setBlue(x, (int) round(yB * 255.0));
        }
    }

    public void Destroy()
    {
        if (ScriptIntrinsicLUT!=null)
            ScriptIntrinsicLUT.destroy();
    }
}
