﻿using CoreGraphics;
using Foundation;
using Metal;
using MetalKit;
using SkiaSharp.Views.iOS;
using UIKit;

namespace DrawnUi.Views
{
    /// <summary>
    /// A Metal-backed SkiaSharp view that renders directly to screen without retained rendering
    /// </summary>
    [Register(nameof(SKMetalViewEnhanced))]
    [DesignTimeVisible(true)]
    public class SKMetalViewEnhanced : MTKView, IMTKViewDelegate, IComponent
    {
        // for IComponent
#pragma warning disable 67
        private event EventHandler DisposedInternal;
#pragma warning restore 67
        ISite IComponent.Site { get; set; }

        event EventHandler IComponent.Disposed
        {
            add { DisposedInternal += value; }
            remove { DisposedInternal -= value; }
        }

        private bool _designMode;
        private IMTLDevice _device;
        private GRMtlBackendContext _backendContext;
        private GRContext _context;
        private SKSize _canvasSize;
        private GCHandle _queuePin;

        /// <summary>
        /// Gets a value indicating whether the view is using manual refresh mode.
        /// </summary>
        public bool ManualRefresh => Paused && EnableSetNeedsDisplay;

        /// <summary>
        /// Gets the current canvas size.
        /// </summary>
        public SKSize CanvasSize => _canvasSize;

        /// <summary>
        /// Gets the SkiaSharp GRContext used for rendering.
        /// </summary>
        public GRContext GRContext => _context;

        /// <summary>
        /// Initializes a new instance of the SKMetalViewEnhanced class.
        /// </summary>
        public SKMetalViewEnhanced()
            : this(CGRect.Empty)
        {
        }

        /// <summary>
        /// Initializes a new instance of the SKMetalViewEnhanced class with the specified frame.
        /// </summary>
        public SKMetalViewEnhanced(CGRect frame)
            : base(frame, null)
        {
            Initialize();
        }

        /// <summary>
        /// Initializes a new instance of the SKMetalViewEnhanced class with the specified frame and device.
        /// </summary>
        public SKMetalViewEnhanced(CGRect frame, IMTLDevice device)
            : base(frame, device)
        {
            Initialize();
        }

        /// <summary>
        /// Initializes a new instance of the SKMetalViewEnhanced class from a native pointer.
        /// </summary>
        public SKMetalViewEnhanced(IntPtr p)
            : base(p)
        {
        }

        /// <summary>
        /// Called when the view is loaded from a nib file.
        /// </summary>
        public override void AwakeFromNib()
        {
            base.AwakeFromNib();
            Initialize();
        }

        /// <summary>
        /// Initializes the Metal rendering context and configuration.
        /// </summary>
        private void Initialize()
        {
            _designMode = ((IComponent)this).Site?.DesignMode == true;

            if (_designMode)
                return;

            _device = Device ?? MTLDevice.SystemDefault;
            if (_device == null)
            {
                Console.WriteLine("Metal is not supported on this device.");
                return;
            }

            ColorPixelFormat = MTLPixelFormat.BGRA8Unorm;
            DepthStencilPixelFormat = MTLPixelFormat.Depth32Float_Stencil8;

            if (DeviceInfo.Current.DeviceType == DeviceType.Virtual)
            {
                DepthStencilStorageMode = MTLStorageMode.Private;
                SampleCount = 4;
            }
            else
            {
                DepthStencilStorageMode = MTLStorageMode.Shared;
                SampleCount = 2;
            }

            FramebufferOnly = false;

            Device = _device;
            _backendContext = new GRMtlBackendContext { Device = _device, Queue = _device.CreateCommandQueue() };

            Delegate = this;

            _queuePin = GCHandle.Alloc(_backendContext.Queue, GCHandleType.Pinned);
        }

        /// <summary>
        /// Called when the drawable size changes.
        /// </summary>
        void IMTKViewDelegate.DrawableSizeWillChange(MTKView view, CGSize size)
        {
            var newSize = size.ToSKSize();
            _canvasSize = newSize;

            if (ManualRefresh)
                SetNeedsDisplay();
        }

        void IMTKViewDelegate.Draw(MTKView view)
        {
            if (_designMode || _backendContext.Queue == null || CurrentDrawable?.Texture == null)
                return;

            _canvasSize = DrawableSize.ToSKSize();
            if (_canvasSize.Width <= 0 || _canvasSize.Height <= 0)
                return;

            _context ??= GRContext.CreateMetal(_backendContext);

            const SKColorType colorType = SKColorType.Bgra8888;
            const GRSurfaceOrigin surfaceOrigin = GRSurfaceOrigin.TopLeft;

            // create the render target
            var metalInfo = new GRMtlTextureInfo(CurrentDrawable.Texture);
            using var renderTarget = new GRBackendRenderTarget((int)CanvasSize.Width, (int)CanvasSize.Height,
                (int)SampleCount, metalInfo);

            // create the surface
            using var surface = SKSurface.Create(_context, renderTarget, surfaceOrigin, colorType);
            using var canvas = surface.Canvas;

            // start drawing
            var e = new SKPaintMetalSurfaceEventArgs(surface, renderTarget, surfaceOrigin, colorType);
            OnPaintSurface(e);

            // flush the SkiaSharp contents
            canvas.Flush();
            surface.Flush();
            _context.Flush();

            // present
            using var commandBuffer = _backendContext.Queue.CommandBuffer();
            commandBuffer.PresentDrawable(CurrentDrawable);
            commandBuffer.Commit();
        }

        public event EventHandler<SKPaintMetalSurfaceEventArgs> PaintSurface;

        /// <summary>
        /// Raises the PaintSurface event.
        /// </summary>
        protected virtual void OnPaintSurface(SKPaintMetalSurfaceEventArgs e)
        {
            PaintSurface?.Invoke(this, e);
        }

        /// <summary>
        /// Forces the view to redraw its contents.
        /// </summary>
        public void InvalidateSurface()
        {
            SetNeedsDisplay();
        }

        /// <summary>
        /// Releases the unmanaged resources used by the SKMetalViewEnhanced and optionally releases the managed resources.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (_queuePin.IsAllocated)
                {
                    _queuePin.Free();
                }

                _context?.Dispose();
                _context = null;
            }

            base.Dispose(disposing);
        }
    }
}
