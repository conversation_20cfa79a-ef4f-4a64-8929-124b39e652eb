﻿<Project Sdk="Microsoft.NET.Sdk">

    <!--using Directory.Build.props-->

    <PropertyGroup>
        <Title>MauiGraphics addon to DrawnUI for .NET MAUI</Title>
        <PackageId>DrawnUi.MauiGraphics</PackageId>
        <Description>SkiaMauiGraphics DrawnUi control for .NET MAUI</Description>
        <PackageTags>maui drawnui skia skiasharp draw</PackageTags>
        <Packable>true</Packable>
        <SymbolPackageFormat>snupkg</SymbolPackageFormat>
        <CreatePackage>false</CreatePackage>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Maui.Graphics.Skia" Version="8.0.40" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\DrawnUi\DrawnUi.Maui.csproj" />
    </ItemGroup>


</Project>