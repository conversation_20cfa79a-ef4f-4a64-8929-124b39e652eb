
#pragma version(1)
#pragma rs java_package_name(com.appomobi.nativetools.graphics)
#pragma rs_fp_relaxed


//---------------------------------------------------------------------------


int random = 0;

// -----------------------------------------------------------------------
// TEST
// -----------------------------------------------------------------------
uchar4 __attribute__((kernel)) fillWithRandom(uint32_t x, uint32_t y)
// -----------------------------------------------------------------------
{
  uchar4 out = {(x + random) % 255, y % 255, random % 255, 255};

  return out;
}

// -----------------------------------------------------------------------
// CONVERT
//------------------------------------------------------------------------------

int32_t width;
int32_t height;
uint picWidth, uvPixelStride, uvRowStride;

rs_allocation ypsIn, uIn, vIn;

// -----------------------------------------------------------------------
// The LaunchOptions ensure that the Kernel does not enter the padding  zone of Y, so yRowStride can be ignored WITHIN the Kernel.
// -----------------------------------------------------------------------
uchar4 __attribute__((kernel)) YuvToRGB(uint32_t x, uint32_t y)
// -----------------------------------------------------------------------
{
// index for accessing the uIn's and vIn's
uint uvIndex=  uvPixelStride * (x/2) + uvRowStride*(y/2);

// get the y,u,v values
uchar yps= rsGetElementAt_uchar(ypsIn, x, y);
uchar u= rsGetElementAt_uchar(uIn, uvIndex);
uchar v= rsGetElementAt_uchar(vIn, uvIndex);

// calc argb
int4 argb;
    argb.r = yps + v * 1436 / 1024 - 179;
    argb.g =  yps -u * 46549 / 131072 + 44 -v * 93604 / 131072 + 91;
    argb.b = yps +u * 1814 / 1024 - 227;
    argb.a = 255;

uchar4 out = convert_uchar4(clamp(argb, 0, 255));
return out;
}

// -----------------------------------------------------------------------
// BLIT
//---------------------------------------------------------------------------
 uchar4 __attribute__((kernel)) blit(uchar4 in, uint32_t x, uint32_t y)
//---------------------------------------------------------------------------
{
  uchar4 out = in;
  out.r = in.r;
  out.g = in.g;
  out.b = in.b;

  return out;
}

// -----------------------------------------------------------------------
// SPLINES
// -----------------------------------------------------------------------
typedef struct __attribute__((packed)) Spline {
   float xs[3];//; //12
   float ys[3];//[3];//12
   float as[2];//[2];//8
   float bs[2];//[2];//8
   uchar tag;//1
} t_Spline;

// -----------------------------------------------------------------------

float gamma = 1.0;
bool invert = false;
bool forceBW = false;

t_Spline splineRed;
t_Spline splineGreen;
t_Spline splineBlue;

// -----------------------------------------------------------------------

const bool logOnce = false;

static float ComputeSplineY (t_Spline spline, float x)
{
 //   if (x==0.0)
 //     return 1.0;
    const int length = 3;

    // Find which spline can be used to compute this x (by simultaneous traverse)
    int j = 0;
    while ((j < length - 2) && (x > spline.xs[j + 1]))
    {
        j++;
    }

    // Evaluate using j'th spline
    float dx = spline.xs[j + 1] - spline.xs[j];

    float t = (x - spline.xs[j]) / dx;

    float y = (1 - t) * spline.ys[j] + t * spline.ys[j + 1] + t * (1 - t) * (spline.as[j] * (1 - t) + spline.bs[j] * t); // equation 9

    if (y<0)
        y=0;

    if (y>1)
        y=1;

    return y;
}


static const float3 kRec709Luma = {0.299f, 0.587f, 0.114f};

// -----------------------------------------------------------------------
uchar4 __attribute__((kernel)) spline(uchar4 in, uint32_t x, uint32_t y)
// -----------------------------------------------------------------------
{
  const float4 inputColor = rsUnpackColor8888(in);

   const float r = ComputeSplineY(splineRed, inputColor.r);
   const float g = ComputeSplineY(splineGreen, inputColor.g);
   const float b = ComputeSplineY(splineBlue, inputColor.b);

  //  if (!logOnce)
  //  {
  //      logOnce=true;
  //      rsDebug("Computing for BLUE: ", inputColor.b);
  //      rsDebug("XS: ", splineBlue.xs[0],splineBlue.xs[1],splineBlue.xs[2]);
  //      rsDebug("Computed: ", b);
  //  }

    const float4 outputColor = (float4){ r,  g,  b, inputColor.a};

    uchar4 out = in;

    if (forceBW)
    {
       const float monochrome = dot(outputColor.rgb, kRec709Luma);
       out = rsPackColorTo8888(monochrome, monochrome, monochrome, inputColor.a);
    }
    else
    {
       out = rsPackColorTo8888(outputColor);
    }

  return out;
}

int rotation=0;
rs_allocation blitInput;

//-----------------------------------------------------------------------
uchar4 __attribute__((kernel)) adjustRotate(uint32_t x, uint32_t y)
//-----------------------------------------------------------------------
{

    uint32_t inX  = x;
    uint32_t inY = y;

    if (rotation==270)
    {
        inX  = width - 1 - y;
        inY = x;
    }
    else
    if (rotation==180) //flipped
    {
        inX  = y;
        inY = x;
    }
    else
    if (rotation==90)
    {
        inX  = y;
        inY = height - 1 - x;
    }

    //rsDebug("Renderscript adjust reading ", inX, inY);

    const uchar4 in = rsGetElementAt_uchar4(blitInput, inX, inY);
    const float4 inputColor = rsUnpackColor8888(in);

    float4 outputColor = inputColor;

    uchar4 out = in;

    if (gamma == 1.0 && !invert && !forceBW)
        return out;

    if (gamma != 1.0)
    {
        //  if (gamma < 0)
        //      gamma = 0;

        float3 corrected = native_powr(inputColor.rgb, gamma);
        outputColor = (float4){ corrected.r, corrected.g, corrected.b, inputColor.a };
    }

    if (forceBW)
    {
       const float monochrome = dot(outputColor.rgb, kRec709Luma);
       outputColor = (float4){ monochrome, monochrome, monochrome, inputColor.a};
    }

     if (invert)
     {
        outputColor = (float4){ 1 - outputColor.r, 1 - outputColor.g, 1 - outputColor.b, outputColor.a };
     }

   out = rsPackColorTo8888(outputColor);
   return out;
}

//-----------------------------------------------------------------------
uchar4 __attribute__((kernel)) adjust(uchar4 in, uint32_t x, uint32_t y)
//-----------------------------------------------------------------------
{
    const float4 inputColor = rsUnpackColor8888(in);

    float4 outputColor = inputColor;

    uchar4 out = in;

    if (gamma == 1.0 && !invert && !forceBW)
        return out;

    if (gamma != 1.0)
    {
        //  if (gamma < 0)
        //      gamma = 0;

        float3 corrected = native_powr(inputColor.rgb, gamma);
        outputColor = (float4){ corrected.r, corrected.g, corrected.b, inputColor.a };
    }

    if (forceBW)
    {
       const float monochrome = dot(outputColor.rgb, kRec709Luma);
       outputColor = (float4){ monochrome, monochrome, monochrome, inputColor.a};
    }

     if (invert)
     {
        outputColor = (float4){ 1 - outputColor.r, 1 - outputColor.g, 1 - outputColor.b, outputColor.a };
     }

   out = rsPackColorTo8888(outputColor);
   return out;
}


//------------------------------------------------------------------------
void init()
// -----------------------------------------------------------------------
{
	//rsDebug("Renderscript init Called ", rsUptimeMillis());
}


// -----------------------------------------------------------------------
int root()
// -----------------------------------------------------------------------
{

	//rsDebug("Renderscript root Called ", rsUptimeMillis());
    return 1;
}
