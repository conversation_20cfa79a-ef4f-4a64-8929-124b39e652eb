<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Advanced Scrolling with SkiaScroll in DrawnUi.Maui | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Advanced Scrolling with SkiaScroll in DrawnUi.Maui | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../../styles/docfx.css">
      <link rel="stylesheet" href="../../styles/main.css">
      <meta property="docfx:navrel" content="../../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../../index.html">
                <img id="logo" class="svg" src="../../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="advanced-scrolling-with-skiascroll-in-drawnuimaui">Advanced Scrolling with SkiaScroll in DrawnUi.Maui</h1>

<p>DrawnUi.Maui’s SkiaScroll control provides high-performance, flexible scrolling for custom UIs, games, dashboards, and data-heavy apps. This article covers advanced usage, virtualization, customization, and best practices for SkiaScroll and related controls.</p>
<h2 id="why-skiascroll">Why SkiaScroll?</h2>
<ul>
<li><strong>Smooth, pixel-perfect scrolling</strong> on all platforms</li>
<li><strong>Supports both vertical, horizontal, and bidirectional scrolling</strong></li>
<li><strong>Virtualization</strong> for large data sets</li>
<li><strong>Customizable headers, footers, and overlays</strong></li>
<li><strong>Pinch-to-zoom and gesture support</strong></li>
<li><strong>Works with any DrawnUi content: layouts, images, shapes, etc.</strong></li>
</ul>
<h2 id="basic-usage">Basic Usage</h2>
<pre><code class="lang-xml">&lt;draw:SkiaScroll Orientation=&quot;Vertical&quot; WidthRequest=&quot;400&quot; HeightRequest=&quot;600&quot;&gt;
    &lt;draw:SkiaLayout Type=&quot;Column&quot; Spacing=&quot;10&quot;&gt;
        &lt;draw:SkiaLabel Text=&quot;Item 1&quot; /&gt;
        &lt;draw:SkiaLabel Text=&quot;Item 2&quot; /&gt;
        &lt;!-- More items --&gt;
    &lt;/draw:SkiaLayout&gt;
&lt;/draw:SkiaScroll&gt;
</code></pre>
<h2 id="multi-directional-and-zoomable-scrolling">Multi-Directional and Zoomable Scrolling</h2>
<pre><code class="lang-xml">&lt;draw:SkiaScroll Orientation=&quot;Both&quot; ZoomLocked=&quot;False&quot; ZoomMin=&quot;1&quot; ZoomMax=&quot;3&quot;&gt;
    &lt;draw:SkiaLayout&gt;
        &lt;draw:SkiaImage Source=&quot;large_map.jpg&quot; /&gt;
    &lt;/draw:SkiaLayout&gt;
&lt;/draw:SkiaScroll&gt;
</code></pre>
<h2 id="virtualization-for-large-data-sets">Virtualization for Large Data Sets</h2>
<p>Enable virtualization for smooth performance with thousands of items:</p>
<pre><code class="lang-xml">&lt;draw:SkiaScroll Virtualisation=&quot;Enabled&quot; Orientation=&quot;Vertical&quot;&gt;
    &lt;draw:SkiaLayout
        Type=&quot;Column&quot;
        ItemsSource=&quot;{Binding LargeItemCollection}&quot;
        Virtualisation=&quot;Enabled&quot;&gt;
        &lt;draw:SkiaLayout.ItemTemplate&gt;
            &lt;DataTemplate&gt;
                &lt;draw:SkiaLabel Text=&quot;{Binding Title}&quot; /&gt;
            &lt;/DataTemplate&gt;
        &lt;/draw:SkiaLayout.ItemTemplate&gt;
    &lt;/draw:SkiaLayout&gt;
&lt;/draw:SkiaScroll&gt;
</code></pre>
<ul>
<li><code>Virtualisation</code> on SkiaScroll controls viewport-based rendering.</li>
<li><code>Virtualisation</code> on SkiaLayout controls the strategy (Enabled, Disabled).</li>
<li>Combine with <code>RecyclingTemplate</code> for template reuse.</li>
<li>Use <code>VirtualisationInflated</code> to control how much content outside the viewport is still rendered.</li>
</ul>
<h2 id="custom-headers-footers-and-overlays">Custom Headers, Footers, and Overlays</h2>
<pre><code class="lang-xml">&lt;draw:SkiaScroll HeaderSticky=&quot;True&quot; HeaderBehind=&quot;False&quot;&gt;
    &lt;draw:SkiaScroll.Header&gt;
        &lt;draw:SkiaShape Type=&quot;Rectangle&quot; BackgroundColor=&quot;#3498DB&quot; HeightRequest=&quot;80&quot;&gt;
            &lt;draw:SkiaLabel Text=&quot;Sticky Header&quot; TextColor=&quot;White&quot; FontSize=&quot;18&quot; /&gt;
        &lt;/draw:SkiaShape&gt;
    &lt;/draw:SkiaScroll.Header&gt;
    &lt;draw:SkiaLayout Type=&quot;Column&quot;&gt;
        &lt;!-- Content --&gt;
    &lt;/draw:SkiaLayout&gt;
    &lt;draw:SkiaScroll.Footer&gt;
        &lt;draw:SkiaShape Type=&quot;Rectangle&quot; BackgroundColor=&quot;#2C3E50&quot; HeightRequest=&quot;60&quot;&gt;
            &lt;draw:SkiaLabel Text=&quot;Footer&quot; TextColor=&quot;White&quot; /&gt;
        &lt;/draw:SkiaShape&gt;
    &lt;/draw:SkiaScroll.Footer&gt;
&lt;/draw:SkiaScroll&gt;
</code></pre>
<h2 id="pull-to-refresh">Pull-to-Refresh</h2>
<pre><code class="lang-xml">&lt;draw:SkiaScroll x:Name=&quot;MyScrollView&quot; Refreshing=&quot;OnRefreshing&quot;&gt;
    &lt;draw:SkiaScroll.RefreshIndicator&gt;
        &lt;draw:RefreshIndicator /&gt;
    &lt;/draw:SkiaScroll.RefreshIndicator&gt;
    &lt;draw:SkiaLayout Type=&quot;Column&quot;&gt;
        &lt;!-- Content items --&gt;
    &lt;/draw:SkiaLayout&gt;
&lt;/draw:SkiaScroll&gt;
</code></pre>
<p>In code-behind:</p>
<pre><code class="lang-csharp">private async void OnRefreshing(object sender, EventArgs e)
{
    // Perform refresh operation
    await LoadDataAsync();
    ((SkiaScroll)sender).EndRefresh();
}
</code></pre>
<h2 id="infinite-and-looped-scrolling">Infinite and Looped Scrolling</h2>
<p>Use SkiaScrollLooped for banners, carousels, or infinite galleries:</p>
<pre><code class="lang-xml">&lt;draw:SkiaScrollLooped Orientation=&quot;Horizontal&quot; IsBanner=&quot;True&quot; CycleSpace=&quot;100&quot;&gt;
    &lt;draw:SkiaLayout Type=&quot;Row&quot;&gt;
        &lt;draw:SkiaImage Source=&quot;image1.jpg&quot; /&gt;
        &lt;draw:SkiaImage Source=&quot;image2.jpg&quot; /&gt;
        &lt;!-- More images --&gt;
    &lt;/draw:SkiaLayout&gt;
&lt;/draw:SkiaScrollLooped&gt;
</code></pre>
<h2 id="programmatic-scrolling-and-position-tracking">Programmatic Scrolling and Position Tracking</h2>
<pre><code class="lang-csharp">// Scroll to a specific position
myScroll.ScrollToPosition(0, 500, true); // Animated scroll to Y=500

// Scroll to a child element
myScroll.ScrollToView(targetElement, true);

// Track scroll position
float y = myScroll.ViewportOffsetY;
</code></pre>
<h2 id="performance-tips">Performance Tips</h2>
<ul>
<li>Enable virtualization for large lists</li>
<li>Use <code>Cache=&quot;Operations&quot;</code> for static or rarely-changing content</li>
<li>Avoid nesting too many scrolls; prefer flat layouts</li>
<li>Use SkiaLabelFps to monitor performance</li>
<li>For custom drawing, override OnDraw in your content controls</li>
</ul>
<h2 id="advanced-custom-scroll-effects-and-gestures">Advanced: Custom Scroll Effects and Gestures</h2>
<ul>
<li>Implement parallax, sticky headers, or custom scroll physics by extending SkiaScroll</li>
<li>Use gesture listeners for advanced input (drag, swipe, pinch)</li>
<li>Combine with SkiaDrawer for overlay panels</li>
</ul>
<h2 id="summary">Summary</h2>
<p>SkiaScroll and related controls provide a robust, high-performance foundation for any scrolling UI in DrawnUi.Maui. With support for virtualization, zoom, custom overlays, and advanced gestures, you can build everything from chat apps to dashboards and games with smooth, responsive scrolling.</p>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/advanced/skiascroll.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../../styles/docfx.js"></script>
    <script type="text/javascript" src="../../styles/main.js"></script>
  </body>
</html>
