<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Layout Controls | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Layout Controls | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../../styles/docfx.css">
      <link rel="stylesheet" href="../../styles/main.css">
      <meta property="docfx:navrel" content="../../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../../index.html">
                <img id="logo" class="svg" src="../../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="layout-controls">Layout Controls</h1>

<p>DrawnUi.Maui provides a powerful and flexible layout system for arranging controls with high performance. The system is similar to MAUI's native layout system but optimized for direct rendering with SkiaSharp.</p>
<h2 id="core-layout-types">Core Layout Types</h2>
<p>DrawnUi.Maui offers several core layout types:</p>
<h3 id="skialayout">SkiaLayout</h3>
<p>The base layout control for measurement, arrangement, and rendering of child elements. It supports different layout strategies via the <code>Type</code> property:</p>
<ul>
<li>Managing child controls</li>
<li>Performance optimizations (see below)</li>
</ul>
<pre><code class="lang-xml">&lt;draw:SkiaLayout
    Type=&quot;Absolute&quot;
    WidthRequest=&quot;400&quot;
    HeightRequest=&quot;300&quot;&gt;
    &lt;!-- Child controls here --&gt;
&lt;/draw:SkiaLayout&gt;
</code></pre>
<h2 id="layout-types">Layout Types</h2>
<p>The <code>Type</code> property on <code>SkiaLayout</code> supports:</p>
<ul>
<li><strong>Absolute</strong>: Free positioning, default. Think of it like a MAUI Grid with a single column and a single row.</li>
<li><strong>Grid</strong>: Row and column-based layout, classic MAUI Grid.</li>
<li><strong>Column</strong>: Vertical stacking (like MAUI VerticalStackLayout)</li>
<li><strong>Row</strong>: Horizontal stacking (like MAUI HorizontalStackLayout)</li>
<li><strong>Wrap</strong>: Items wrap to new lines when space runs out (similar to WPF Stackpanel)</li>
</ul>
<pre><code class="lang-xml">&lt;draw:SkiaLayout Type=&quot;Wrap&quot; Spacing=&quot;5&quot;&gt;
    &lt;draw:SkiaLabel Text=&quot;Item 1&quot; /&gt;
    &lt;draw:SkiaLabel Text=&quot;Item 2&quot; /&gt;
    &lt;!-- More items --&gt;
&lt;/draw:SkiaLayout&gt;
</code></pre>
<blockquote>
<p><strong>Note</strong>: The <code>Spacing</code> property takes a single double value that applies to both horizontal and vertical spacing between items.</p>
</blockquote>
<h2 id="specialized-layout-controls">Specialized Layout Controls</h2>
<h3 id="contentlayout">ContentLayout</h3>
<p>A specialized layout for hosting a single content element is <code>ContentLayout</code>, <code>SkiaShape</code> is subclassing it to be able to contain a single child inside a <code>Content</code> property, instead of using <code>Children</code>.</p>
<h3 id="snappinglayout">SnappingLayout</h3>
<p>Supports snap points for controlled scrolling, ideal for carousels or paginated interfaces. <code>SkiaDrawer</code>, <code>SkiaCarousel</code> are deriving from it.</p>
<h2 id="example-creating-a-grid-layout">Example: Creating a Grid Layout</h2>
<pre><code class="lang-xml">&lt;draw:SkiaLayout Type=&quot;Grid&quot;
    ColumnDefinitions=&quot;Auto,*,100&quot;
    RowDefinitions=&quot;Auto,*,50&quot;&gt;
    &lt;!-- Header spanning all columns --&gt;
    &lt;draw:SkiaLabel 
        Text=&quot;Grid Header&quot; 
        Column=&quot;0&quot; 
        ColumnSpan=&quot;3&quot;
        Row=&quot;0&quot; 
        HorizontalOptions=&quot;Center&quot; /&gt;
    &lt;!-- Sidebar --&gt;
    &lt;draw:SkiaLayout
        Type=&quot;Column&quot;
        Column=&quot;0&quot;
        Row=&quot;1&quot;
        RowSpan=&quot;2&quot;
        BackgroundColor=&quot;LightGray&quot;
        Padding=&quot;10&quot;&gt;
        &lt;draw:SkiaLabel Text=&quot;Menu Item 1&quot; /&gt;
        &lt;draw:SkiaLabel Text=&quot;Menu Item 2&quot; /&gt;
        &lt;draw:SkiaLabel Text=&quot;Menu Item 3&quot; /&gt;
    &lt;/draw:SkiaLayout&gt;
    &lt;!-- Main content --&gt;
    &lt;draw:ContentLayout 
        Column=&quot;1&quot; 
        Row=&quot;1&quot;&gt;
        &lt;draw:SkiaLabel 
            Text=&quot;Main Content Area&quot; 
            HorizontalOptions=&quot;Center&quot; 
            VerticalOptions=&quot;Center&quot; /&gt;
    &lt;/draw:ContentLayout&gt;
    &lt;!-- Right panel --&gt;
    &lt;draw:SkiaLayout
        Type=&quot;Column&quot;
        Column=&quot;2&quot;
        Row=&quot;1&quot;
        BackgroundColor=&quot;LightBlue&quot;
        Padding=&quot;5&quot;&gt;
        &lt;draw:SkiaLabel Text=&quot;Panel Info&quot; /&gt;
    &lt;/draw:SkiaLayout&gt;
    &lt;!-- Footer spanning columns 1-2 --&gt;
    &lt;draw:SkiaLabel 
        Text=&quot;Footer&quot; 
        Column=&quot;1&quot; 
        ColumnSpan=&quot;2&quot;
        Row=&quot;2&quot; 
        HorizontalOptions=&quot;Center&quot;
        VerticalOptions=&quot;Center&quot; /&gt;
&lt;/draw:SkiaLayout&gt;
</code></pre>
<p>All grid functionality is handled by SkiaLayout with <code>Type=&quot;Grid&quot;</code>.</p>
<h2 id="under-the-hood">Under the Hood</h2>
<p>The layout system is built on top of the <code>SkiaControl</code> base class. Layout controls extend this for child management, measurement, and arrangement. Internally, structures like <code>LayoutStructure</code> and <code>GridStructure</code> efficiently track and manage layout information.</p>
<h3 id="caching">Caching</h3>
<p>Caching options help balance CPU and memory usage:</p>
<ul>
<li><strong>None</strong>: No caching, recalculated every frame</li>
<li><strong>Operations</strong>: Caches drawing commands</li>
<li><strong>Image</strong>: Caches as bitmap (more memory, less CPU)</li>
<li><strong>GPU</strong>: Uses hardware acceleration where available</li>
</ul>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/layouts.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../../styles/docfx.js"></script>
    <script type="text/javascript" src="../../styles/main.js"></script>
  </body>
</html>
