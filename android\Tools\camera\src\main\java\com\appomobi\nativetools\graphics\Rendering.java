package com.appomobi.nativetools.graphics;

import android.app.Activity;
import android.content.res.AssetFileDescriptor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.media.Image;
import android.renderscript.Allocation;
import android.renderscript.Element;
import android.renderscript.RenderScript;
import android.renderscript.Script;
import android.renderscript.ScriptIntrinsicLUT;
import android.renderscript.Type;
import android.util.Log;

import java.io.FileInputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;


public final class Rendering {



    public static final Random RANDOM = new Random();

    public static boolean Verbose = false;

    public static void TestOutput(RenderScript rs, Allocation outTexture)
    {
        Log.d("TestOutput", "launched");

        if (outTexture==null){
            Log.d("TestOutput", "outTexture is NULL");
            return;
        }

        Log.d("TestOutput", outTexture.toString());

        // Create the script
        ScriptC_Scripts renderer = new ScriptC_Scripts(rs);

        int t = RANDOM.nextInt();

        renderer.set_random(t);

        if (Verbose)
            Log.d("TestOutput", "received "+outTexture.getBytesSize()+" bytes");

        Log.d("TestOutput", "starting foreach..");

        renderer.forEach_fillWithRandom(outTexture);

        //renderer.forEach_fillWithRandom(outTexture);

        Log.d("TestOutput", "foreach success");

        outTexture.ioSend();

        renderer.destroy();

    }

    //-----------------------------------------------------------------------
    // BLIT
    //-----------------------------------------------------------------------
    public static boolean BlitYuv420888toRGB(RenderScript rs, Image image, Allocation outTexture) throws UserException
    {

        if (outTexture==null){
            if (Verbose)
                Log.d("BlitYuv420888toRGB", "outTexture is NULL");
            return false;
        }

        //----------STEP 1 to RGB----------------------
        int height = image.getHeight();
        int width = image.getWidth();

        if (Verbose)
        Log.d("BlitYuv420888toRGB", "launched");

        ScriptC_Scripts renderer = new ScriptC_Scripts (rs);

        if (Verbose)
            Log.d("BlitYuv420888toRGB", "created script");

        Allocation rgbTexture = CreateRgbAllocationFromYUV420888(rs, renderer, image);

        if (Verbose)
            Log.d("BlitYuv420888toRGB", "created rgbTexture");

        //outAlloc.syncAll(0);

        //throw new UserException("DEBUG from "+outAlloc.getBytesSize()+" out "+drawSurface.getBytesSize()+"", "");

        //----------STEP 4 BLIT!----------------------

        //todo size check for debug

        if (Verbose)
            Log.d("BlitYuv420888toRGB", "input "+outTexture.getBytesSize()+" -> out "+outTexture.getBytesSize()+" bytes");


        renderer.forEach_blit(rgbTexture, outTexture);

        outTexture.ioSend();

        rgbTexture.destroy();

        renderer.destroy();

        return true;
    }

    //-----------------------------------------------------------------------
    // CONVERT
    //-----------------------------------------------------------------------
    public static Allocation CreateRgbAllocationFromYUV420888(RenderScript rs, ScriptC_Scripts renderer, Image image)
    {
        if (Verbose)
            Log.d("CreateRgbAllocation", "launched");

        int height = image.getHeight();
        int width = image.getWidth();

        if (Verbose)
            Log.d("CreateRgbAllocation", "assembling buffer..");

        // Get the three image planes
        Image.Plane[] planes = image.getPlanes();
        ByteBuffer buffer = planes[0].getBuffer();
        byte[] y = new byte[buffer.remaining()];
        buffer.get(y);

        buffer = planes[1].getBuffer();
        byte[] u = new byte[buffer.remaining()];
        buffer.get(u);

        buffer = planes[2].getBuffer();
        byte[] v = new byte[buffer.remaining()];
        buffer.get(v);

        // get the relevant RowStrides and PixelStrides
        // (we know from documentation that PixelStride is 1 for y)
        int yRowStride= planes[0].getRowStride();
        int uvRowStride= planes[1].getRowStride();  // we know from   documentation that RowStride is the same for u and v.
        int uvPixelStride= planes[1].getPixelStride();  // we know from   documentation that PixelStride is the same for u and v.


        // Y,U,V are defined as global allocations, the out-Allocation is the Bitmap.
        // Note also that uAlloc and vAlloc are 1-dimensional while yAlloc is 2-dimensional.
        Type.Builder typeUcharY = new Type.Builder(rs, Element.U8(rs));

        //typeUcharY.setX(yRowStride).setY(height);
        typeUcharY.setX(yRowStride).setY(y.length / yRowStride);

        Allocation yAlloc = Allocation.createTyped(rs, typeUcharY.create(),
                Allocation.USAGE_SCRIPT | Allocation.USAGE_GRAPHICS_CONSTANTS | Allocation.USAGE_GRAPHICS_TEXTURE );



        //***************crashes below*****************


        yAlloc.copyFrom(y); // <--- array too small for allocation type
        //yAlloc.copy1DRangeFrom(0, y.length, y);


        //***************crashes above*****************


        renderer.set_ypsIn(yAlloc);

        Type.Builder typeUcharUV = new Type.Builder(rs, Element.U8(rs));
        // note that the size of the u's and v's are as follows:
        //      (  (width/2)*PixelStride + padding  ) * (height/2)
        // =    (RowStride                          ) * (height/2)
        // but I noted that on the S7 it is 1 less...
        typeUcharUV.setX(u.length);
        Allocation uAlloc = Allocation.createTyped(rs, typeUcharUV.create(),
                Allocation.USAGE_SCRIPT | Allocation.USAGE_GRAPHICS_CONSTANTS | Allocation.USAGE_GRAPHICS_TEXTURE );
        uAlloc.copyFrom(u);
        renderer.set_uIn(uAlloc);

        Allocation vAlloc = Allocation.createTyped(rs, typeUcharUV.create(),
                Allocation.USAGE_SCRIPT | Allocation.USAGE_GRAPHICS_CONSTANTS | Allocation.USAGE_GRAPHICS_TEXTURE );
        vAlloc.copyFrom(v);
        renderer.set_vIn(vAlloc);

        // handover parameters
        renderer.set_picWidth(width);
        renderer.set_uvRowStride (uvRowStride);
        renderer.set_uvPixelStride (uvPixelStride);

    //Bitmap outBitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);

    //    Allocation outAlloc = Allocation.createFromBitmap(rs, outBitmap, Allocation.MipmapControl.MIPMAP_NONE,
    //            Allocation.USAGE_SCRIPT | Allocation.USAGE_GRAPHICS_CONSTANTS | Allocation.USAGE_GRAPHICS_TEXTURE );

        Type.Builder rgbaType = new Type.Builder(rs, Element.RGBA_8888(rs)).setX(width).setY(height);

        Allocation outAlloc = Allocation.createTyped(rs, rgbaType.create(), Allocation.MipmapControl.MIPMAP_NONE,
            Allocation.USAGE_SCRIPT | Allocation.USAGE_GRAPHICS_CONSTANTS | Allocation.USAGE_GRAPHICS_TEXTURE);

        if (Verbose)
            Log.d("CreateRgbAllocation", "created yuvBitmap"+ width +"x"+height);

        Script.LaunchOptions lo = new Script.LaunchOptions();

        lo.setX(0, width);  // by this we ignore the y’s padding zone, i.e. the right side of x between width and yRowStride

        lo.setY(0, y.length / yRowStride);

        if (Verbose)
            Log.d("CreateRgbAllocation", "launching ForEach..");

        //***************crashes below*****************

        renderer.forEach_YuvToRGB(outAlloc, lo);

        //***************crashes above*****************

        if (Verbose)
            Log.d("CreateRgbAllocation", "ForEach success!");


        yAlloc.destroy();
        uAlloc.destroy();
        vAlloc.destroy();

        y = null;
        u = null;
        v = null;
        buffer=null;

        if (Verbose)
            Log.d("CreateRgbAllocation", "returning..");

        return outAlloc;
    }

    //-----------------------------------------------------------------------
    // SPLINES
    //-----------------------------------------------------------------------

    static ScriptField_Spline.Item ToRenderer(ChannelSpline spline)
    {
        ScriptField_Spline.Item ret = new ScriptField_Spline.Item();

        ret.as = spline.as;
        ret.bs = spline.bs;
        ret.xs = spline.xs;
        ret.ys = spline.ys;

        ret.tag = (short)spline.tag;

        return ret;
    }

    static String currentPreset;
    static Map<String, ChannelSplinePreset> _presets = new HashMap<String, ChannelSplinePreset>();

    public static List<String> GetAvailableSplinePresets()
    {
        List<String> stringArray = new ArrayList<>();
        for (Map.Entry<String, ChannelSplinePreset> entry : _presets.entrySet())
        {
            stringArray.add(entry.getKey());
        }
        return stringArray;
    }

    //As title. For caching data.
    // True - was already existing and updated, False - created new
    public static boolean AddUpdateSplinesPreset(RenderScript rs, String id, ChannelSpline red, ChannelSpline green, ChannelSpline blue)
    {
        ChannelSplinePreset preset = _presets.get(id);
        boolean ret = true;
        if (preset == null)
        {
            //create new
            ret = false;
        }
        else
        {
            preset.Destroy(); //todo can crash if in the middle of processing..
        }

        //updated
        preset = new ChannelSplinePreset();
        preset.id = id;
        preset.splineR = red;
        preset.splineG = green;
        preset.splineB = blue;

        //legacy compat
        preset.cacheR = ToRenderer(red);
        preset.cacheG = ToRenderer(green);
        preset.cacheB = ToRenderer(blue);

        //haha run on steroids
        preset.CreateLUT(rs);

        _presets.put(id, preset);

        if (ret==true)
        {
            currentPreset=null;
            return true;
        }

        return false;
    }

    //-----------------------------------------------------------------------
    public static boolean BlitWithPresetYuv420888toRGB(RenderScript rs,
                                                        Image image,
                                                        Allocation outTexture,
                                                        String presetId,
                                                        float gamma,
                                                        boolean bw) throws UserException
    //-----------------------------------------------------------------------
    {
        ChannelSplinePreset preset = _presets.get(presetId);
        if (preset == null)
        {
            //oops
            throw new UserException("Preset not found: "+presetId, new Throwable("Oops!"));
            //return false;
        }

        //----------STEP 1 to RGB----------------------
        int height = image.getHeight();
        int width = image.getWidth();

        ScriptC_Scripts renderer = new ScriptC_Scripts(rs);

        //create input
        Allocation rgbTexture = CreateRgbAllocationFromYUV420888(rs, renderer, image);

        if (Verbose)
            Log.d("BlitWithFilters", "created rgbTexture");

        //----------STEP 2 BLIT with options --------

        preset.ScriptIntrinsicLUT.forEach(rgbTexture, outTexture);

        //renderer.set_forceBW(bw);
        //renderer.set_gamma(gamma);

//        renderer.set_splineRed(preset.cacheR);
//        renderer.set_splineGreen(preset.cacheG);
//        renderer.set_splineBlue(preset.cacheB);

//        renderer.forEach_spline(rgbTexture, outTexture);

        //---------- CLEANUP --------
        outTexture.ioSend();

        rgbTexture.destroy();

        renderer.destroy();

        return true;
    }
//-----------------------------------------------------------------------
public static boolean BlitWithLUT(RenderScript rs,
                               ScriptC_Scripts renderer,
                               ScriptIntrinsicLUT rendererLut,
                               Image image,
                               Allocation outTexture,
                               int rotation,
                               float gamma) throws UserException
//-----------------------------------------------------------------------
{
        //----------STEP 1 to RGB----------------------

        //create input
        Allocation rgbTexture = CreateRgbAllocationFromYUV420888(rs, renderer, image);

        //----------STEP 2 BLIT with options --------

        Allocation lutTexture = Allocation.createTyped(rs, rgbTexture.getType(),
            Allocation.USAGE_SCRIPT);

    if (Verbose)
        Log.d("BlitWithLUT", "applying lut..");


        rendererLut.forEach(rgbTexture, lutTexture);

    if (Verbose)
        Log.d("BlitWithLUT", "rendererLut success");



        renderer.set_invert(false);
        renderer.set_forceBW(false);
        renderer.set_gamma(gamma);

        int height = image.getHeight();
        int width = image.getWidth();
        renderer.set_width(width);
        renderer.set_height(height);


    if (Verbose)
        Log.d("BlitWithLUT", "Launching adjust..");

    if (rotation!=0)
    {
        renderer.set_blitInput(lutTexture);
        renderer.set_rotation(rotation);
        renderer.forEach_adjustRotate(outTexture);
    }
    else
        renderer.forEach_adjust(lutTexture, outTexture);

    if (Verbose)
        Log.d("BlitWithLUT", "adjust ok");

    //---------- CLEANUP --------
        if ((outTexture.getUsage() & Allocation.USAGE_IO_OUTPUT) == Allocation.USAGE_IO_OUTPUT)
            outTexture.ioSend();

        lutTexture.destroy();
        rgbTexture.destroy();

        return true;
    }

    //-----------------------------------------------------------------------
public static boolean BlitAdjust(RenderScript rs,
                                      ScriptC_Scripts renderer,
                                      Image image,
                                      Allocation outTexture,
                                      int rotation,
                                      float gamma,
                                      boolean bw,
                                      boolean invert) throws UserException
//-----------------------------------------------------------------------
{
        //----------STEP 1 to RGB----------------------

        //create input
        Allocation rgbTexture = CreateRgbAllocationFromYUV420888(rs, renderer, image);

        //----------STEP 2 BLIT with options --------

        renderer.set_invert(invert);
        renderer.set_forceBW(bw);
        renderer.set_gamma(gamma);

        int height = image.getHeight();
        int width = image.getWidth();
        renderer.set_width(width);
        renderer.set_height(height);

    if (rotation!=0)
    {
        renderer.set_rotation(rotation);
        renderer.set_blitInput(rgbTexture);
        renderer.forEach_adjustRotate(outTexture);
    }
    else
        renderer.forEach_adjust(rgbTexture, outTexture);


        //---------- CLEANUP --------
        if ((outTexture.getUsage() & Allocation.USAGE_IO_OUTPUT) == Allocation.USAGE_IO_OUTPUT)
            outTexture.ioSend();

        rgbTexture.destroy();

        return true;
    }

    public static boolean BlitWithFiltersYuv420888toRGB(RenderScript rs,
                                                        Image image,
                                                        Allocation outTexture,
                                                        ChannelSpline red,
                                                        ChannelSpline green,
                                                        ChannelSpline blue,
                                                        float gamma,
                                                        boolean bw) throws UserException
    {
        //----------STEP 1 to RGB----------------------
        int height = image.getHeight();
        int width = image.getWidth();

        ScriptC_Scripts renderer = new ScriptC_Scripts(rs);

        //create input
        Allocation rgbTexture = CreateRgbAllocationFromYUV420888(rs, renderer, image);

        if (Verbose)
            Log.d("BlitWithFilters", "created rgbTexture");

        //----------STEP 2 BLIT with options --------

        renderer.set_forceBW(bw);

        renderer.set_gamma(gamma);

        renderer.set_splineRed(ToRenderer(red));
        renderer.set_splineGreen(ToRenderer(green));
        renderer.set_splineBlue(ToRenderer(blue));

        renderer.forEach_spline(rgbTexture, outTexture);

        //---------- CLEANUP --------
        outTexture.ioSend();

        rgbTexture.destroy();

        renderer.destroy();

        return true;
    }


}
