<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Drawer Controls | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Drawer Controls | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../../styles/docfx.css">
      <link rel="stylesheet" href="../../styles/main.css">
      <meta property="docfx:navrel" content="../../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../../index.html">
                <img id="logo" class="svg" src="../../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="drawer-controls">Drawer Controls</h1>

<p>DrawnUi.Maui provides powerful drawer controls for creating sliding panels that can appear from any edge of the screen. This article covers the drawer components available in the framework.</p>
<h2 id="skiadrawer">SkiaDrawer</h2>
<p>SkiaDrawer is a versatile control that provides a sliding panel (drawer) with animated transitions and gesture support. It can slide in from any edge, making it perfect for navigation menus, filter panels, property drawers, and more.</p>
<h3 id="basic-usage">Basic Usage</h3>
<pre><code class="lang-xml">&lt;DrawUi:SkiaDrawer
    Direction=&quot;FromBottom&quot;
    HeaderSize=&quot;60&quot;
    IsOpen=&quot;False&quot;
    HeightRequest=&quot;500&quot;
    HorizontalOptions=&quot;Fill&quot;
    VerticalOptions=&quot;End&quot;&gt;
    
    &lt;DrawUi:SkiaLayout
        HorizontalOptions=&quot;Fill&quot;
        VerticalOptions=&quot;Fill&quot;&gt;
        
        &lt;!-- Header (visible when drawer is closed) --&gt;
        &lt;DrawUi:SkiaShape
            BackgroundColor=&quot;Blue&quot;
            CornerRadius=&quot;20,20,0,0&quot;
            HeightRequest=&quot;60&quot;
            HorizontalOptions=&quot;Fill&quot;&gt;
            
            &lt;DrawUi:SkiaLabel
                Text=&quot;Drag Me&quot;
                TextColor=&quot;White&quot;
                HorizontalOptions=&quot;Center&quot;
                VerticalOptions=&quot;Center&quot; /&gt;
                
        &lt;/DrawUi:SkiaShape&gt;
        
        &lt;!-- Content (scrolls within drawer) --&gt;
        &lt;DrawUi:SkiaLayout
            BackgroundColor=&quot;White&quot;
            Padding=&quot;20&quot;
            Type=&quot;Column&quot;
            Spacing=&quot;16&quot;
            AddMarginTop=&quot;60&quot;&gt;
            
            &lt;DrawUi:SkiaLabel
                Text=&quot;Drawer Content&quot;
                FontSize=&quot;20&quot;
                TextColor=&quot;Black&quot; /&gt;
                
            &lt;!-- Additional content --&gt;
                
        &lt;/DrawUi:SkiaLayout&gt;
        
    &lt;/DrawUi:SkiaLayout&gt;
    
&lt;/DrawUi:SkiaDrawer&gt;
</code></pre>
<h3 id="key-properties">Key Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Direction</code></td>
<td>DrawerDirection</td>
<td>Direction from which the drawer appears</td>
</tr>
<tr>
<td><code>HeaderSize</code></td>
<td>double</td>
<td>Size of the area that remains visible when drawer is closed</td>
</tr>
<tr>
<td><code>IsOpen</code></td>
<td>bool</td>
<td>Controls whether the drawer is open or closed</td>
</tr>
<tr>
<td><code>AmplitudeSize</code></td>
<td>double</td>
<td>Optional override for drawer movement calculation</td>
</tr>
</tbody>
</table>
<h3 id="drawer-direction">Drawer Direction</h3>
<p>The <code>Direction</code> property controls the edge from which the drawer appears:</p>
<pre><code class="lang-xml">&lt;!-- Bottom drawer --&gt;
&lt;DrawUi:SkiaDrawer
    Direction=&quot;FromBottom&quot;
    VerticalOptions=&quot;End&quot;&gt;
    &lt;!-- Content --&gt;
&lt;/DrawUi:SkiaDrawer&gt;

&lt;!-- Top drawer --&gt;
&lt;DrawUi:SkiaDrawer
    Direction=&quot;FromTop&quot;
    VerticalOptions=&quot;Start&quot;&gt;
    &lt;!-- Content --&gt;
&lt;/DrawUi:SkiaDrawer&gt;

&lt;!-- Left drawer --&gt;
&lt;DrawUi:SkiaDrawer
    Direction=&quot;FromLeft&quot;
    HorizontalOptions=&quot;Start&quot;&gt;
    &lt;!-- Content --&gt;
&lt;/DrawUi:SkiaDrawer&gt;

&lt;!-- Right drawer --&gt;
&lt;DrawUi:SkiaDrawer
    Direction=&quot;FromRight&quot;
    HorizontalOptions=&quot;End&quot;&gt;
    &lt;!-- Content --&gt;
&lt;/DrawUi:SkiaDrawer&gt;
</code></pre>
<p>Note that you should set the appropriate alignment options (<code>VerticalOptions</code> and <code>HorizontalOptions</code>) to match the drawer direction.</p>
<h3 id="header-and-content">Header and Content</h3>
<p>The drawer typically consists of two main parts:</p>
<ul>
<li><strong>Header</strong>: Remains partially or fully visible when the drawer is closed</li>
<li><strong>Content</strong>: The main body of the drawer that slides in and out</li>
</ul>
<p>The <code>HeaderSize</code> property determines how much of the drawer remains visible when closed.</p>
<h3 id="controlling-the-drawer">Controlling the Drawer</h3>
<p>You can control the drawer programmatically:</p>
<pre><code class="lang-csharp">// Open the drawer
myDrawer.IsOpen = true;

// Close the drawer
myDrawer.IsOpen = false;

// Toggle the drawer
myDrawer.IsOpen = !myDrawer.IsOpen;
</code></pre>
<p>You can also use binding:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaDrawer
    IsOpen=&quot;{Binding IsDrawerOpen, Mode=TwoWay}&quot;&gt;
    &lt;!-- Content --&gt;
&lt;/DrawUi:SkiaDrawer&gt;
</code></pre>
<h3 id="commands">Commands</h3>
<p>SkiaDrawer provides built-in commands for programmatic control:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaButton
    Text=&quot;Open Drawer&quot;
    CommandTapped=&quot;{Binding Source={x:Reference MyDrawer}, Path=CommandOpen}&quot; /&gt;
    
&lt;DrawUi:SkiaButton
    Text=&quot;Close Drawer&quot;
    CommandTapped=&quot;{Binding Source={x:Reference MyDrawer}, Path=CommandClose}&quot; /&gt;
    
&lt;DrawUi:SkiaButton
    Text=&quot;Toggle Drawer&quot;
    CommandTapped=&quot;{Binding Source={x:Reference MyDrawer}, Path=CommandToggle}&quot; /&gt;
</code></pre>
<h3 id="scrollable-drawer-content">Scrollable Drawer Content</h3>
<p>For scrollable content within the drawer, combine with SkiaScroll:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaDrawer
    x:Name=&quot;BottomDrawer&quot;
    Direction=&quot;FromBottom&quot;
    HeaderSize=&quot;60&quot;
    HeightRequest=&quot;500&quot;
    VerticalOptions=&quot;End&quot;&gt;
    
    &lt;DrawUi:SkiaLayout&gt;
        &lt;!-- Header --&gt;
        &lt;DrawUi:SkiaShape
            BackgroundColor=&quot;Blue&quot;
            HeightRequest=&quot;60&quot;
            CornerRadius=&quot;20,20,0,0&quot;&gt;
            &lt;!-- Header content --&gt;
        &lt;/DrawUi:SkiaShape&gt;
        
        &lt;!-- Scrollable content --&gt;
        &lt;DrawUi:SkiaScroll
            AddMarginTop=&quot;60&quot;
            Bounces=&quot;False&quot;
            BackgroundColor=&quot;White&quot;&gt;
            
            &lt;DrawUi:SkiaLayout
                Type=&quot;Column&quot;
                Padding=&quot;20&quot;
                Spacing=&quot;16&quot;&gt;
                &lt;!-- Many items here --&gt;
            &lt;/DrawUi:SkiaLayout&gt;
            
        &lt;/DrawUi:SkiaScroll&gt;
    &lt;/DrawUi:SkiaLayout&gt;
    
&lt;/DrawUi:SkiaDrawer&gt;
</code></pre>
<p>The <code>AddMarginTop</code> property on SkiaScroll helps create space for the header.</p>
<h2 id="examples">Examples</h2>
<h3 id="bottom-sheet-with-form">Bottom Sheet with Form</h3>
<pre><code class="lang-xml">&lt;DrawUi:SkiaDrawer
    Direction=&quot;FromBottom&quot;
    HeaderSize=&quot;60&quot;
    IsOpen=&quot;False&quot;
    HeightRequest=&quot;400&quot;
    VerticalOptions=&quot;End&quot;
    HorizontalOptions=&quot;Fill&quot;&gt;
    
    &lt;DrawUi:SkiaLayout&gt;
        &lt;!-- Header --&gt;
        &lt;DrawUi:SkiaShape
            BackgroundColor=&quot;#3498DB&quot;
            CornerRadius=&quot;20,20,0,0&quot;
            HeightRequest=&quot;60&quot;&gt;
            
            &lt;DrawUi:SkiaLayout
                HorizontalOptions=&quot;Fill&quot;
                VerticalOptions=&quot;Fill&quot;&gt;
                
                &lt;DrawUi:SkiaLabel
                    Text=&quot;Settings&quot;
                    TextColor=&quot;White&quot;
                    FontSize=&quot;18&quot;
                    HorizontalOptions=&quot;Center&quot;
                    VerticalOptions=&quot;Center&quot; /&gt;
                    
                &lt;DrawUi:SkiaShape
                    Type=&quot;Rectangle&quot;
                    WidthRequest=&quot;40&quot;
                    HeightRequest=&quot;4&quot;
                    CornerRadius=&quot;2&quot;
                    BackgroundColor=&quot;White&quot;
                    Margin=&quot;0,10,0,0&quot;
                    HorizontalOptions=&quot;Center&quot;
                    VerticalOptions=&quot;Start&quot; /&gt;
                    
            &lt;/DrawUi:SkiaLayout&gt;
            
        &lt;/DrawUi:SkiaShape&gt;
        
        &lt;!-- Content --&gt;
        &lt;DrawUi:SkiaScroll
            AddMarginTop=&quot;60&quot;
            BackgroundColor=&quot;White&quot;&gt;
            
            &lt;DrawUi:SkiaLayout
                Type=&quot;Column&quot;
                Padding=&quot;20&quot;
                Spacing=&quot;16&quot;&gt;
                
                &lt;!-- Form fields --&gt;
                &lt;DrawUi:SkiaLabel
                    Text=&quot;Username&quot;
                    FontSize=&quot;14&quot;
                    TextColor=&quot;#333333&quot; /&gt;
                    
                &lt;DrawUi:SkiaMauiEntry
                    PlaceholderText=&quot;Enter your username&quot;
                    HeightRequest=&quot;50&quot;
                    BackgroundColor=&quot;#F5F5F5&quot;
                    TextColor=&quot;#333333&quot; /&gt;
                    
                &lt;DrawUi:SkiaLabel
                    Text=&quot;Email&quot;
                    FontSize=&quot;14&quot;
                    TextColor=&quot;#333333&quot; /&gt;
                    
                &lt;DrawUi:SkiaMauiEntry
                    PlaceholderText=&quot;Enter your email&quot;
                    HeightRequest=&quot;50&quot;
                    BackgroundColor=&quot;#F5F5F5&quot;
                    TextColor=&quot;#333333&quot; /&gt;
                    
                &lt;DrawUi:SkiaButton
                    Text=&quot;SAVE&quot;
                    BackgroundColor=&quot;#2ECC71&quot;
                    TextColor=&quot;White&quot;
                    HeightRequest=&quot;50&quot;
                    Margin=&quot;0,20,0,0&quot; /&gt;
                    
            &lt;/DrawUi:SkiaLayout&gt;
            
        &lt;/DrawUi:SkiaScroll&gt;
    &lt;/DrawUi:SkiaLayout&gt;
    
&lt;/DrawUi:SkiaDrawer&gt;
</code></pre>
<h3 id="navigation-drawer">Navigation Drawer</h3>
<pre><code class="lang-xml">&lt;Grid&gt;
    &lt;!-- Main content --&gt;
    &lt;DrawUi:Canvas
        HorizontalOptions=&quot;Fill&quot;
        VerticalOptions=&quot;Fill&quot;&gt;
        
        &lt;DrawUi:SkiaLayout
            BackgroundColor=&quot;White&quot;
            HorizontalOptions=&quot;Fill&quot;
            VerticalOptions=&quot;Fill&quot;&gt;
            
            &lt;!-- Main app content here --&gt;
            
            &lt;DrawUi:SkiaButton
                Text=&quot;Open Menu&quot;
                CommandTapped=&quot;{Binding Source={x:Reference SideDrawer}, Path=CommandOpen}&quot;
                HorizontalOptions=&quot;Start&quot;
                VerticalOptions=&quot;Start&quot;
                Margin=&quot;20&quot; /&gt;
                
        &lt;/DrawUi:SkiaLayout&gt;
        
    &lt;/DrawUi:Canvas&gt;
    
    &lt;!-- Left side drawer --&gt;
    &lt;DrawUi:SkiaDrawer
        x:Name=&quot;SideDrawer&quot;
        Direction=&quot;FromLeft&quot;
        HeaderSize=&quot;0&quot;
        IsOpen=&quot;False&quot;
        WidthRequest=&quot;280&quot;
        HorizontalOptions=&quot;Start&quot;
        VerticalOptions=&quot;Fill&quot;&gt;
        
        &lt;DrawUi:SkiaLayout
            BackgroundColor=&quot;#2C3E50&quot;
            HorizontalOptions=&quot;Fill&quot;
            VerticalOptions=&quot;Fill&quot;&gt;
            
            &lt;DrawUi:SkiaScroll&gt;
                &lt;DrawUi:SkiaLayout
                    Type=&quot;Column&quot;
                    Padding=&quot;0,40,0,0&quot;&gt;
                    
                    &lt;!-- User info --&gt;
                    &lt;DrawUi:SkiaLayout
                        Type=&quot;Column&quot;
                        Padding=&quot;20&quot;
                        Spacing=&quot;8&quot;&gt;
                        
                        &lt;DrawUi:SkiaShape
                            Type=&quot;Circle&quot;
                            WidthRequest=&quot;80&quot;
                            HeightRequest=&quot;80&quot;
                            BackgroundColor=&quot;#3498DB&quot;&gt;
                            
                            &lt;DrawUi:SkiaLabel
                                Text=&quot;JD&quot;
                                FontSize=&quot;36&quot;
                                TextColor=&quot;White&quot;
                                HorizontalOptions=&quot;Center&quot;
                                VerticalOptions=&quot;Center&quot; /&gt;
                                
                        &lt;/DrawUi:SkiaShape&gt;
                        
                        &lt;DrawUi:SkiaLabel
                            Text=&quot;John Doe&quot;
                            FontSize=&quot;18&quot;
                            TextColor=&quot;White&quot;
                            Margin=&quot;0,10,0,0&quot; /&gt;
                            
                        &lt;DrawUi:SkiaLabel
                            Text=&quot;<EMAIL>&quot;
                            FontSize=&quot;14&quot;
                            TextColor=&quot;#BBBBBB&quot; /&gt;
                            
                    &lt;/DrawUi:SkiaLayout&gt;
                    
                    &lt;!-- Menu items --&gt;
                    &lt;DrawUi:SkiaShape
                        Type=&quot;Rectangle&quot;
                        HeightRequest=&quot;1&quot;
                        BackgroundColor=&quot;#405060&quot;
                        Margin=&quot;0,20,0,20&quot; /&gt;
                        
                    &lt;!-- Menu item 1 --&gt;
                    &lt;DrawUi:SkiaHotspot Tapped=&quot;OnMenuItemTapped&quot;&gt;
                        &lt;DrawUi:SkiaLayout
                            Type=&quot;Row&quot;
                            Padding=&quot;20,15&quot;
                            Spacing=&quot;16&quot;&gt;
                            
                            &lt;DrawUi:SkiaShape
                                Type=&quot;Rectangle&quot;
                                WidthRequest=&quot;24&quot;
                                HeightRequest=&quot;24&quot;
                                BackgroundColor=&quot;#3498DB&quot; /&gt;
                                
                            &lt;DrawUi:SkiaLabel
                                Text=&quot;Home&quot;
                                FontSize=&quot;16&quot;
                                TextColor=&quot;White&quot; /&gt;
                                
                        &lt;/DrawUi:SkiaLayout&gt;
                    &lt;/DrawUi:SkiaHotspot&gt;
                    
                    &lt;!-- Additional menu items --&gt;
                    
                &lt;/DrawUi:SkiaLayout&gt;
            &lt;/DrawUi:SkiaScroll&gt;
            
        &lt;/DrawUi:SkiaLayout&gt;
        
    &lt;/DrawUi:SkiaDrawer&gt;
&lt;/Grid&gt;
</code></pre>
<h2 id="performance-considerations">Performance Considerations</h2>
<ul>
<li>For complex drawers, consider using <code>Cache=&quot;Operations&quot;</code> on content that doesn't change often</li>
<li>Use appropriate header size to ensure smooth gestures in the grabbable area</li>
<li>For large drawers with many child elements, enable virtualization in nested scrolling content</li>
<li>Avoid doing heavy work in <code>IsOpen</code> change handlers as this can cause animation stuttering</li>
</ul>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/drawers.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../../styles/docfx.js"></script>
    <script type="text/javascript" src="../../styles/main.js"></script>
  </body>
</html>
