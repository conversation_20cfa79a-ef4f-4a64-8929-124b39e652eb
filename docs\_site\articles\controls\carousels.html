<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Carousel Controls | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Carousel Controls | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../../styles/docfx.css">
      <link rel="stylesheet" href="../../styles/main.css">
      <meta property="docfx:navrel" content="../../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../../index.html">
                <img id="logo" class="svg" src="../../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="carousel-controls">Carousel Controls</h1>

<p>DrawnUi.Maui provides powerful carousel controls for creating interactive, swipeable displays of content. This article covers the carousel components available in the framework.</p>
<h2 id="skiacarousel">SkiaCarousel</h2>
<p>SkiaCarousel is a specialized scroll control designed specifically for creating swipeable carousels with automatic snapping to items.</p>
<h3 id="basic-usage">Basic Usage</h3>
<pre><code class="lang-xml">&lt;DrawUi:SkiaCarousel
    WidthRequest=&quot;400&quot;
    HeightRequest=&quot;200&quot;
    SelectedIndex=&quot;0&quot;&gt;
    
    &lt;!-- Item 1 --&gt;
    &lt;DrawUi:SkiaLayout BackgroundColor=&quot;Red&quot;&gt;
        &lt;DrawUi:SkiaLabel 
            Text=&quot;Slide 1&quot; 
            FontSize=&quot;24&quot; 
            HorizontalOptions=&quot;Center&quot; 
            VerticalOptions=&quot;Center&quot; /&gt;
    &lt;/DrawUi:SkiaLayout&gt;
    
    &lt;!-- Item 2 --&gt;
    &lt;DrawUi:SkiaLayout BackgroundColor=&quot;Green&quot;&gt;
        &lt;DrawUi:SkiaLabel 
            Text=&quot;Slide 2&quot; 
            FontSize=&quot;24&quot; 
            HorizontalOptions=&quot;Center&quot; 
            VerticalOptions=&quot;Center&quot; /&gt;
    &lt;/DrawUi:SkiaLayout&gt;
    
    &lt;!-- Item 3 --&gt;
    &lt;DrawUi:SkiaLayout BackgroundColor=&quot;Blue&quot;&gt;
        &lt;DrawUi:SkiaLabel 
            Text=&quot;Slide 3&quot; 
            FontSize=&quot;24&quot; 
            HorizontalOptions=&quot;Center&quot; 
            VerticalOptions=&quot;Center&quot; /&gt;
    &lt;/DrawUi:SkiaLayout&gt;
    
&lt;/DrawUi:SkiaCarousel&gt;
</code></pre>
<h3 id="key-properties">Key Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>SelectedIndex</code></td>
<td>int</td>
<td>Current selected item index</td>
</tr>
<tr>
<td><code>InTransition</code></td>
<td>bool</td>
<td>Indicates if carousel is currently transitioning</td>
</tr>
<tr>
<td><code>Spacing</code></td>
<td>float</td>
<td>Space between carousel items</td>
</tr>
<tr>
<td><code>SidesOffset</code></td>
<td>float</td>
<td>Side padding to create a peek effect</td>
</tr>
<tr>
<td><code>Bounces</code></td>
<td>bool</td>
<td>Enables bouncing effect at edges</td>
</tr>
<tr>
<td><code>ItemsSource</code></td>
<td>IEnumerable</td>
<td>Data source for dynamically generating items</td>
</tr>
<tr>
<td><code>ItemTemplate</code></td>
<td>DataTemplate</td>
<td>Template for items when using ItemsSource</td>
</tr>
</tbody>
</table>
<h3 id="peek-nextprevious-items">Peek Next/Previous Items</h3>
<p>You can create a peek effect to show portions of adjacent slides:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaCarousel
    WidthRequest=&quot;400&quot;
    HeightRequest=&quot;200&quot;
    SidesOffset=&quot;40&quot;
    SelectedIndex=&quot;0&quot;&gt;
    
    &lt;!-- Items here --&gt;
    
&lt;/DrawUi:SkiaCarousel&gt;
</code></pre>
<p>With <code>SidesOffset=&quot;40&quot;</code>, 40 pixels on each side will be reserved to show portions of the previous and next items.</p>
<h3 id="data-binding">Data Binding</h3>
<p>SkiaCarousel supports data binding through <code>ItemsSource</code> and <code>ItemTemplate</code>:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaCarousel
    WidthRequest=&quot;400&quot;
    HeightRequest=&quot;200&quot;
    ItemsSource=&quot;{Binding CarouselItems}&quot;&gt;
    
    &lt;DrawUi:SkiaCarousel.ItemTemplate&gt;
        &lt;DataTemplate&gt;
            &lt;DrawUi:SkiaLayout BackgroundColor=&quot;{Binding Color}&quot;&gt;
                &lt;DrawUi:SkiaLabel 
                    Text=&quot;{Binding Title}&quot; 
                    FontSize=&quot;24&quot; 
                    HorizontalOptions=&quot;Center&quot; 
                    VerticalOptions=&quot;Center&quot; /&gt;
            &lt;/DrawUi:SkiaLayout&gt;
        &lt;/DataTemplate&gt;
    &lt;/DrawUi:SkiaCarousel.ItemTemplate&gt;
    
&lt;/DrawUi:SkiaCarousel&gt;
</code></pre>
<h3 id="tracking-current-item">Tracking Current Item</h3>
<p>You can bind to the current item or monitor transitions:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaCarousel
    x:Name=&quot;MyCarousel&quot;
    SelectedIndex=&quot;{Binding CurrentIndex, Mode=TwoWay}&quot;
    WidthRequest=&quot;400&quot;
    HeightRequest=&quot;200&quot;&gt;
    
    &lt;!-- Items here --&gt;
    
&lt;/DrawUi:SkiaCarousel&gt;

&lt;!-- Display current state --&gt;
&lt;DrawUi:SkiaLabel 
    Text=&quot;{Binding Source={x:Reference MyCarousel}, Path=SelectedIndex, StringFormat='Current: {0}'}&quot; 
    TextColor=&quot;Black&quot; /&gt;

&lt;DrawUi:SkiaLabel 
    Text=&quot;{Binding Source={x:Reference MyCarousel}, Path=InTransition, StringFormat='In Transition: {0}'}&quot; 
    TextColor=&quot;Black&quot; /&gt;
</code></pre>
<p>The <code>InTransition</code> property is particularly useful for disabling user interactions during transitions.</p>
<h3 id="programmatic-control">Programmatic Control</h3>
<p>You can control the carousel programmatically:</p>
<pre><code class="lang-csharp">// Jump to a specific index
myCarousel.SelectedIndex = 2;

// Animate to a specific index
myCarousel.ScrollTo(2, true);

// Track selection changes
myCarousel.PropertyChanged += (sender, e) =&gt; {
    if (e.PropertyName == nameof(SkiaCarousel.SelectedIndex))
    {
        // Handle selection change
        var index = myCarousel.SelectedIndex;
    }
};
</code></pre>
<h2 id="advanced-examples">Advanced Examples</h2>
<h3 id="image-gallery-carousel">Image Gallery Carousel</h3>
<pre><code class="lang-xml">&lt;DrawUi:SkiaCarousel
    WidthRequest=&quot;400&quot;
    HeightRequest=&quot;300&quot;
    SidesOffset=&quot;50&quot;
    Bounces=&quot;True&quot;&gt;
    
    &lt;DrawUi:SkiaShape Type=&quot;Rectangle&quot; BackgroundColor=&quot;White&quot; CornerRadius=&quot;12&quot;&gt;
        &lt;DrawUi:SkiaShape.Shadows&gt;
            &lt;DrawUi:SkiaShadow Color=&quot;#40000000&quot; BlurRadius=&quot;10&quot; Offset=&quot;0,4&quot; /&gt;
        &lt;/DrawUi:SkiaShape.Shadows&gt;
        &lt;DrawUi:SkiaImage Source=&quot;image1.jpg&quot; Aspect=&quot;AspectFill&quot; /&gt;
    &lt;/DrawUi:SkiaShape&gt;
    
    &lt;DrawUi:SkiaShape Type=&quot;Rectangle&quot; BackgroundColor=&quot;White&quot; CornerRadius=&quot;12&quot;&gt;
        &lt;DrawUi:SkiaShape.Shadows&gt;
            &lt;DrawUi:SkiaShadow Color=&quot;#40000000&quot; BlurRadius=&quot;10&quot; Offset=&quot;0,4&quot; /&gt;
        &lt;/DrawUi:SkiaShape.Shadows&gt;
        &lt;DrawUi:SkiaImage Source=&quot;image2.jpg&quot; Aspect=&quot;AspectFill&quot; /&gt;
    &lt;/DrawUi:SkiaShape&gt;
    
    &lt;DrawUi:SkiaShape Type=&quot;Rectangle&quot; BackgroundColor=&quot;White&quot; CornerRadius=&quot;12&quot;&gt;
        &lt;DrawUi:SkiaShape.Shadows&gt;
            &lt;DrawUi:SkiaShadow Color=&quot;#40000000&quot; BlurRadius=&quot;10&quot; Offset=&quot;0,4&quot; /&gt;
        &lt;/DrawUi:SkiaShape.Shadows&gt;
        &lt;DrawUi:SkiaImage Source=&quot;image3.jpg&quot; Aspect=&quot;AspectFill&quot; /&gt;
    &lt;/DrawUi:SkiaShape&gt;
    
&lt;/DrawUi:SkiaCarousel&gt;
</code></pre>
<h3 id="card-carousel-with-indicators">Card Carousel with Indicators</h3>
<pre><code class="lang-xml">&lt;DrawUi:SkiaLayout LayoutType=&quot;Column&quot; HorizontalOptions=&quot;Fill&quot;&gt;
    
    &lt;DrawUi:SkiaCarousel
        x:Name=&quot;CardCarousel&quot;
        WidthRequest=&quot;400&quot;
        HeightRequest=&quot;300&quot;
        SelectedIndex=&quot;{Binding CurrentCardIndex, Mode=TwoWay}&quot;&gt;
        
        &lt;!-- Card items here --&gt;
        
    &lt;/DrawUi:SkiaCarousel&gt;
    
    &lt;!-- Page indicators --&gt;
    &lt;DrawUi:SkiaLayout 
        LayoutType=&quot;Row&quot; 
        Spacing=&quot;8&quot; 
        HorizontalOptions=&quot;Center&quot;
        Margin=&quot;0,16,0,0&quot;&gt;
        
        &lt;DrawUi:SkiaShape 
            Type=&quot;Circle&quot; 
            WidthRequest=&quot;12&quot; 
            HeightRequest=&quot;12&quot; 
            BackgroundColor=&quot;{Binding Source={x:Reference CardCarousel}, Path=SelectedIndex, Converter={StaticResource SelectedIndexConverter}, ConverterParameter=0}&quot; /&gt;
        
        &lt;DrawUi:SkiaShape 
            Type=&quot;Circle&quot; 
            WidthRequest=&quot;12&quot; 
            HeightRequest=&quot;12&quot; 
            BackgroundColor=&quot;{Binding Source={x:Reference CardCarousel}, Path=SelectedIndex, Converter={StaticResource SelectedIndexConverter}, ConverterParameter=1}&quot; /&gt;
        
        &lt;DrawUi:SkiaShape 
            Type=&quot;Circle&quot; 
            WidthRequest=&quot;12&quot; 
            HeightRequest=&quot;12&quot; 
            BackgroundColor=&quot;{Binding Source={x:Reference CardCarousel}, Path=SelectedIndex, Converter={StaticResource SelectedIndexConverter}, ConverterParameter=2}&quot; /&gt;
        
    &lt;/DrawUi:SkiaLayout&gt;
    
&lt;/DrawUi:SkiaLayout&gt;
</code></pre>
<p>With a converter to change color based on selection:</p>
<pre><code class="lang-csharp">public class SelectedIndexConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        int selectedIndex = (int)value;
        int targetIndex = int.Parse(parameter.ToString());
        
        return selectedIndex == targetIndex ? Colors.Blue : Colors.LightGray;
    }
    
    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
</code></pre>
<h2 id="performance-considerations">Performance Considerations</h2>
<ul>
<li>For optimal performance, use <code>Cache=&quot;Operations&quot;</code> or <code>Cache=&quot;Image&quot;</code> on complex carousel items</li>
<li>Avoid placing too many items directly in the carousel; use virtualization through <code>ItemsSource</code> for large collections</li>
<li>Consider using lightweight content for peek items if they'll be partially visible most of the time</li>
<li>Monitor the performance using <code>SkiaLabelFps</code> during development to ensure smooth scrolling</li>
</ul>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/carousels.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../../styles/docfx.js"></script>
    <script type="text/javascript" src="../../styles/main.js"></script>
  </body>
</html>
