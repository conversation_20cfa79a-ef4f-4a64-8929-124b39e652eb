### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs
  commentId: T:DrawnUi.Draw.SkiaControl.ControlTappedEventArgs
  id: SkiaControl.ControlTappedEventArgs
  parent: DrawnU<PERSON>.Draw
  children:
  - DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.#ctor(System.Object,DrawnUi.Draw.SkiaGesturesParameters,DrawnUi.Draw.GestureEventProcessingInfo)
  - DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.Control
  - DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.Parameters
  - DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.ProcessingInfo
  langs:
  - csharp
  - vb
  name: SkiaControl.ControlTappedEventArgs
  nameWithType: SkiaControl.ControlTappedEventArgs
  fullName: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Base/SkiaControl.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ControlTappedEventArgs
    path: ../src/Shared/Draw/Base/SkiaControl.Shared.cs
    startLine: 1351
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public class SkiaControl.ControlTappedEventArgs : EventArgs'
    content.vb: Public Class SkiaControl.ControlTappedEventArgs Inherits EventArgs
  inheritance:
  - System.Object
  - System.EventArgs
  inheritedMembers:
  - System.EventArgs.Empty
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.Control
  commentId: P:DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.Control
  id: Control
  parent: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs
  langs:
  - csharp
  - vb
  name: Control
  nameWithType: SkiaControl.ControlTappedEventArgs.Control
  fullName: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.Control
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Base/SkiaControl.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Control
    path: ../src/Shared/Draw/Base/SkiaControl.Shared.cs
    startLine: 1353
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public object Control { get; set; }
    parameters: []
    return:
      type: System.Object
    content.vb: Public Property Control As Object
  overload: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.Control*
- uid: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.Parameters
  commentId: P:DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.Parameters
  id: Parameters
  parent: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs
  langs:
  - csharp
  - vb
  name: Parameters
  nameWithType: SkiaControl.ControlTappedEventArgs.Parameters
  fullName: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.Parameters
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Base/SkiaControl.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Parameters
    path: ../src/Shared/Draw/Base/SkiaControl.Shared.cs
    startLine: 1354
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SkiaGesturesParameters Parameters { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.SkiaGesturesParameters
    content.vb: Public Property Parameters As SkiaGesturesParameters
  overload: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.Parameters*
- uid: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.ProcessingInfo
  commentId: P:DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.ProcessingInfo
  id: ProcessingInfo
  parent: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs
  langs:
  - csharp
  - vb
  name: ProcessingInfo
  nameWithType: SkiaControl.ControlTappedEventArgs.ProcessingInfo
  fullName: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.ProcessingInfo
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Base/SkiaControl.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ProcessingInfo
    path: ../src/Shared/Draw/Base/SkiaControl.Shared.cs
    startLine: 1355
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public GestureEventProcessingInfo ProcessingInfo { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.GestureEventProcessingInfo
    content.vb: Public Property ProcessingInfo As GestureEventProcessingInfo
  overload: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.ProcessingInfo*
- uid: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.#ctor(System.Object,DrawnUi.Draw.SkiaGesturesParameters,DrawnUi.Draw.GestureEventProcessingInfo)
  commentId: M:DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.#ctor(System.Object,DrawnUi.Draw.SkiaGesturesParameters,DrawnUi.Draw.GestureEventProcessingInfo)
  id: '#ctor(System.Object,DrawnUi.Draw.SkiaGesturesParameters,DrawnUi.Draw.GestureEventProcessingInfo)'
  parent: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs
  langs:
  - csharp
  - vb
  name: ControlTappedEventArgs(object, SkiaGesturesParameters, GestureEventProcessingInfo)
  nameWithType: SkiaControl.ControlTappedEventArgs.ControlTappedEventArgs(object, SkiaGesturesParameters, GestureEventProcessingInfo)
  fullName: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.ControlTappedEventArgs(object, DrawnUi.Draw.SkiaGesturesParameters, DrawnUi.Draw.GestureEventProcessingInfo)
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Base/SkiaControl.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Base/SkiaControl.Shared.cs
    startLine: 1357
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public ControlTappedEventArgs(object control, SkiaGesturesParameters args, GestureEventProcessingInfo info)
    parameters:
    - id: control
      type: System.Object
    - id: args
      type: DrawnUi.Draw.SkiaGesturesParameters
    - id: info
      type: DrawnUi.Draw.GestureEventProcessingInfo
    content.vb: Public Sub New(control As Object, args As SkiaGesturesParameters, info As GestureEventProcessingInfo)
  overload: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.#ctor*
  nameWithType.vb: SkiaControl.ControlTappedEventArgs.New(Object, SkiaGesturesParameters, GestureEventProcessingInfo)
  fullName.vb: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.New(Object, DrawnUi.Draw.SkiaGesturesParameters, DrawnUi.Draw.GestureEventProcessingInfo)
  name.vb: New(Object, SkiaGesturesParameters, GestureEventProcessingInfo)
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.EventArgs
  commentId: T:System.EventArgs
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.eventargs
  name: EventArgs
  nameWithType: EventArgs
  fullName: System.EventArgs
- uid: System.EventArgs.Empty
  commentId: F:System.EventArgs.Empty
  parent: System.EventArgs
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.eventargs.empty
  name: Empty
  nameWithType: EventArgs.Empty
  fullName: System.EventArgs.Empty
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.Control*
  commentId: Overload:DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.Control
  href: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.html#DrawnUi_Draw_SkiaControl_ControlTappedEventArgs_Control
  name: Control
  nameWithType: SkiaControl.ControlTappedEventArgs.Control
  fullName: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.Control
- uid: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.Parameters*
  commentId: Overload:DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.Parameters
  href: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.html#DrawnUi_Draw_SkiaControl_ControlTappedEventArgs_Parameters
  name: Parameters
  nameWithType: SkiaControl.ControlTappedEventArgs.Parameters
  fullName: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.Parameters
- uid: DrawnUi.Draw.SkiaGesturesParameters
  commentId: T:DrawnUi.Draw.SkiaGesturesParameters
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaGesturesParameters.html
  name: SkiaGesturesParameters
  nameWithType: SkiaGesturesParameters
  fullName: DrawnUi.Draw.SkiaGesturesParameters
- uid: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.ProcessingInfo*
  commentId: Overload:DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.ProcessingInfo
  href: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.html#DrawnUi_Draw_SkiaControl_ControlTappedEventArgs_ProcessingInfo
  name: ProcessingInfo
  nameWithType: SkiaControl.ControlTappedEventArgs.ProcessingInfo
  fullName: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.ProcessingInfo
- uid: DrawnUi.Draw.GestureEventProcessingInfo
  commentId: T:DrawnUi.Draw.GestureEventProcessingInfo
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.GestureEventProcessingInfo.html
  name: GestureEventProcessingInfo
  nameWithType: GestureEventProcessingInfo
  fullName: DrawnUi.Draw.GestureEventProcessingInfo
- uid: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.#ctor*
  commentId: Overload:DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.#ctor
  href: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.html#DrawnUi_Draw_SkiaControl_ControlTappedEventArgs__ctor_System_Object_DrawnUi_Draw_SkiaGesturesParameters_DrawnUi_Draw_GestureEventProcessingInfo_
  name: ControlTappedEventArgs
  nameWithType: SkiaControl.ControlTappedEventArgs.ControlTappedEventArgs
  fullName: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.ControlTappedEventArgs
  nameWithType.vb: SkiaControl.ControlTappedEventArgs.New
  fullName.vb: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.New
  name.vb: New
