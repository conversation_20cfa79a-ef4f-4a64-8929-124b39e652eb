﻿/ This SKSL shader converts the input image to grayscale.

// Declare a uniform shader named 'input'.
// In SKSL, this is how you receive the source image or content as an input.
// SkiaSharp will automatically bind the source to this 'input' shader.
uniform shader input;

// The main function is the entry point for your fragment shader.
// It is executed for each pixel (fragment) being rendered.
void main() {
    // 1. Get the color of the current pixel from the input source.
    //    'sk_FragCoord.xy' is a built-in SKSL variable that provides
    //    the 2D coordinates of the current pixel being processed.
    //    'input.eval()' samples the 'input' shader at these coordinates
    //    to get its vec4 (RGBA) color.
    vec4 src_color = input.eval(sk_FragCoord.xy);

    // 2. Calculate the luminance (grayscale value) using a weighted average
    //    of the red, green, and blue components of the source color.
    //    These specific weights (0.2126, 0.7152, 0.0722) are standard
    //    for converting sRGB to perceived luminance, accounting for
    //    human eye sensitivity to different colors.
    float luminance = dot(src_color.rgb, vec3(0.2126, 0.7152, 0.0722));

    // 3. Construct the final output color for the current pixel.
    //    'sk_FragColor' is a built-in SKSL output variable where
    //    you assign the final color for the fragment.
    //    We set the red, green, and blue channels to the calculated 'luminance'
    //    and preserve the original alpha channel from the source color.
    sk_FragColor = vec4(luminance, luminance, luminance, src_color.a);
}
