﻿#if WINDOWS || MACCATALYST || IOS || ANDROID
using Microsoft.Maui.Handlers;

namespace DrawnUi.Views
{
    /// <summary>
    /// SHARED
    /// </summary>
    public partial class SKGLViewHandlerEnhanced
    {
        public static PropertyMapper<ISKGLView, SKGLViewHandlerEnhanced> SKGLViewMapper =
            new PropertyMapper<ISKGLView, SKGLViewHandlerEnhanced>(ViewHandler.ViewMapper)
            {
                [nameof(ISKGLView.EnableTouchEvents)] = MapEnableTouchEvents,
                [nameof(ISKGLView.IgnorePixelScaling)] = MapIgnorePixelScaling,
                [nameof(ISKGLView.HasRenderLoop)] = MapHasRenderLoop,
#if WINDOWS
				[nameof(ISKGLView.Background)] = MapBackground,
#endif
            };

        public static CommandMapper<ISKGLView, SKGLViewHandlerEnhanced> SKGLViewCommandMapper =
            new CommandMapper<ISKGLView, SKGLViewHandlerEnhanced>(ViewHandler.ViewCommandMapper)
            {
                [nameof(ISKGLView.InvalidateSurface)] = OnInvalidateSurface,
            };

        public SKGLViewHandlerEnhanced()
            : base(SKGLViewMapper, SKGLViewCommandMapper)
        {
        }

        public SKGLViewHandlerEnhanced(PropertyMapper? mapper, CommandMapper? commands)
            : base(mapper ?? SKGLViewMapper, commands ?? SKGLViewCommandMapper)
        {
        }
    }
}
#endif