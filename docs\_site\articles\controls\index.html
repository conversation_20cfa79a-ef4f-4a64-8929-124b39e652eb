<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Controls Overview | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Controls Overview | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../../styles/docfx.css">
      <link rel="stylesheet" href="../../styles/main.css">
      <meta property="docfx:navrel" content="../../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../../index.html">
                <img id="logo" class="svg" src="../../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="controls-overview">Controls Overview</h1>

<p>DrawnUi positions itsself as an angine providing a toolset to create and use custom drawn controls. Out-of-the box it provides you with base controls that can be used a lego-bricks to composite custom controls, and proposes some useful pre-made custom controls.</p>
<p>The main spirit is to have all controlls subclassable and customizable at the maximum possible extent.</p>
<p>DrawnUi provides a comprehensive set of UI controls rendered with SkiaSharp for optimal performance. All controls support platform-specific styling and extensive customization options.</p>
<h2 id="control-categories">Control Categories</h2>
<p>DrawnUi controls are organized into several categories:</p>
<h3 id="button-controls">Button Controls</h3>
<ul>
<li><a href="buttons.html">SkiaButton</a>: Standard button with platform-specific styling</li>
<li><a href="buttons.html#variants">Custom button variants</a>: Outlined, text-only, and other button styles</li>
</ul>
<h3 id="toggle-controls">Toggle Controls</h3>
<ul>
<li><a href="switches.html#skiaswitch">SkiaSwitch</a>: Platform-styled toggle switch</li>
<li><a href="switches.html#skiacheckbox">SkiaCheckbox</a>: Platform-styled checkbox</li>
<li><a href="switches.html#skiatoggle">SkiaToggle</a>: Base toggle class for custom toggles</li>
</ul>
<h3 id="layout-controls">Layout Controls</h3>
<ul>
<li><a href="layouts.html#skialayout">SkiaLayout</a>: Base layout container</li>
<li><a href="layouts.html#gridlayout">GridLayout</a>: Grid-based layout</li>
<li><a href="layouts.html#stack">HStack/VStack</a>: Horizontal and vertical stack layouts</li>
</ul>
<h3 id="text-controls">Text Controls</h3>
<ul>
<li><a href="text.html#skialabel">SkiaLabel</a>: High-performance text rendering</li>
<li><a href="text.html#skiamarkdownlabel">SkiaMarkdownLabel</a>: Markdown-capable text control</li>
</ul>
<h3 id="image-controls">Image Controls</h3>
<ul>
<li><a href="images.html#skiaimage">SkiaImage</a>: High-performance image rendering</li>
<li><a href="images.html#skiasvg">SkiaSvg</a>: SVG rendering</li>
<li><a href="images.html#skiagif">SkiaGif</a>: Animated GIF support</li>
</ul>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/index.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../../styles/docfx.js"></script>
    <script type="text/javascript" src="../../styles/main.js"></script>
  </body>
</html>
