<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Advanced Gesture Handling in DrawnUi.Maui | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Advanced Gesture Handling in DrawnUi.Maui | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../../styles/docfx.css">
      <link rel="stylesheet" href="../../styles/main.css">
      <meta property="docfx:navrel" content="../../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../../index.html">
                <img id="logo" class="svg" src="../../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="advanced-gesture-handling-in-drawnuimaui">Advanced Gesture Handling in DrawnUi.Maui</h1>

<p>DrawnUi.Maui provides a robust and extensible gesture system for building interactive, touch-driven UIs. This article covers how to use built-in gestures, implement custom gesture logic, and best practices for advanced scenarios.</p>
<h2 id="gesture-system-overview">Gesture System Overview</h2>
<ul>
<li><strong>Unified gesture model</strong> for tap, drag, swipe, pinch, long-press, and multi-touch</li>
<li><strong>ISkiaGestureListener</strong> interface for custom gesture handling</li>
<li><strong>SkiaHotspot</strong> and gesture listeners for declarative and code-based gestures</li>
<li><strong>Gesture locking and propagation control</strong> for complex UI hierarchies</li>
</ul>
<h2 id="basic-tap-and-click-handling">Basic Tap and Click Handling</h2>
<p>Use <code>SkiaHotspot</code> for simple tap/click detection:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaHotspot Tapped=&quot;OnTapped&quot;&gt;
    &lt;DrawUi:SkiaShape Type=&quot;Circle&quot; BackgroundColor=&quot;Blue&quot; WidthRequest=&quot;80&quot; HeightRequest=&quot;80&quot; /&gt;
&lt;/DrawUi:SkiaHotspot&gt;
</code></pre>
<pre><code class="lang-csharp">private void OnTapped(object sender, EventArgs e)
{
    // Handle tap
}
</code></pre>
<h2 id="handling-drag-swipe-and-multi-touch">Handling Drag, Swipe, and Multi-Touch</h2>
<p>Implement <code>ISkiaGestureListener</code> for advanced gestures:</p>
<pre><code class="lang-csharp">public class DraggableShape : SkiaShape, ISkiaGestureListener
{
    private float _x, _y;
    public override void OnParentChanged()
    {
        base.OnParentChanged();
        RegisterGestureListener(this);
    }
    public bool OnGestureEvent(TouchActionType type, TouchActionEventArgs args, TouchActionResult result)
    {
        if (type == TouchActionType.Pan)
        {
            _x = args.Location.X;
            _y = args.Location.Y;
            Invalidate();
            return true;
        }
        return false;
    }
}
</code></pre>
<h2 id="gesture-locking-and-propagation">Gesture Locking and Propagation</h2>
<p>Use the <code>LockChildrenGestures</code> property to control gesture propagation:</p>
<ul>
<li><code>LockTouch.Enabled</code>: Prevents children from receiving gestures</li>
<li><code>LockTouch.Disabled</code>: Allows all gestures to propagate</li>
<li><code>LockTouch.PassTap</code>: Only tap events pass through</li>
<li><code>LockTouch.PassTapAndLongPress</code>: Tap and long-press pass through</li>
</ul>
<p>Example:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaLayout LockChildrenGestures=&quot;PassTap&quot;&gt;
    &lt;!-- Only tap gestures reach children --&gt;
&lt;/DrawUi:SkiaLayout&gt;
</code></pre>
<h2 id="custom-gesture-handling-in-code">Custom Gesture Handling in Code</h2>
<p>Override <code>OnGestureEvent</code> for fine-grained control:</p>
<pre><code class="lang-csharp">public override ISkiaGestureListener OnGestureEvent(TouchActionType type, TouchActionEventArgs args, TouchActionResult result, SKPoint childOffset, SKPoint childOffsetDirect)
{
    // Custom logic for gesture routing or handling
    return base.OnGestureEvent(type, args, result, childOffset, childOffsetDirect);
}
</code></pre>
<h2 id="multi-touch-and-pinch-to-zoom">Multi-Touch and Pinch-to-Zoom</h2>
<p>Listen for pinch and multi-touch events:</p>
<pre><code class="lang-csharp">public bool OnGestureEvent(TouchActionType type, TouchActionEventArgs args, TouchActionResult result)
{
    if (type == TouchActionType.Pinch)
    {
        // args.PinchScale, args.Center, etc.
        // Handle zoom
        return true;
    }
    return false;
}
</code></pre>
<h2 id="gesture-utilities-and-best-practices">Gesture Utilities and Best Practices</h2>
<ul>
<li>Use <code>HadInput</code> to track which listeners have received input</li>
<li>Use <code>InputTransparent</code> to make controls ignore gestures</li>
<li>For performance, avoid deep gesture listener hierarchies</li>
<li>Use debug logging to trace gesture flow</li>
</ul>
<h2 id="example-swipe-to-delete-list-item">Example: Swipe-to-Delete List Item</h2>
<pre><code class="lang-xml">&lt;DrawUi:SkiaLayout ItemsSource=&quot;{Binding Items}&quot;&gt;
    &lt;DrawUi:SkiaLayout.ItemTemplate&gt;
        &lt;DataTemplate&gt;
            &lt;local:SwipeToDeleteItem /&gt;
        &lt;/DataTemplate&gt;
    &lt;/DrawUi:SkiaLayout.ItemTemplate&gt;
&lt;/DrawUi:SkiaLayout&gt;
</code></pre>
<pre><code class="lang-csharp">public class SwipeToDeleteItem : SkiaLayout, ISkiaGestureListener
{
    public override void OnParentChanged()
    {
        base.OnParentChanged();
        RegisterGestureListener(this);
    }
    public bool OnGestureEvent(TouchActionType type, TouchActionEventArgs args, TouchActionResult result)
    {
        if (type == TouchActionType.Pan &amp;&amp; result == TouchActionResult.Panning)
        {
            // Move item horizontally
            this.TranslationX = args.Location.X;
            Invalidate();
            return true;
        }
        if (type == TouchActionType.Pan &amp;&amp; result == TouchActionResult.Up)
        {
            if (Math.Abs(this.TranslationX) &gt; 100)
            {
                // Trigger delete
                // ...
            }
            this.TranslationX = 0;
            Invalidate();
            return true;
        }
        return false;
    }
}
</code></pre>
<h2 id="debugging-and-extending-gestures">Debugging and Extending Gestures</h2>
<ul>
<li>Use debug output to trace gesture events and propagation</li>
<li>Extend or compose gesture listeners for complex scenarios</li>
<li>Integrate with platform-specific gesture APIs if needed</li>
</ul>
<h2 id="summary">Summary</h2>
<p>DrawnUi.Maui’s gesture system enables rich, interactive UIs with tap, drag, swipe, pinch, and custom gestures. Use SkiaHotspot for simple cases, ISkiaGestureListener for advanced logic, and gesture locking for complex layouts.</p>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/advanced/gestures.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../../styles/docfx.js"></script>
    <script type="text/javascript" src="../../styles/main.js"></script>
  </body>
</html>
