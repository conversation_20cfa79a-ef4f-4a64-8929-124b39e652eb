<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>SkiaShape | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="SkiaShape | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../../styles/docfx.css">
      <link rel="stylesheet" href="../../styles/main.css">
      <meta property="docfx:navrel" content="../../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../../index.html">
                <img id="logo" class="svg" src="../../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="skiashape">SkiaShape</h1>

<p>SkiaShape is a versatile control for rendering various geometric shapes in DrawnUi.Maui. Unlike traditional shape controls, SkiaShape offers high-performance rendering through SkiaSharp while supporting advanced features like custom paths, shadows, gradients, and content hosting.</p>
<h2 id="basic-usage">Basic Usage</h2>
<p>SkiaShape supports various shape types through its <code>Type</code> property:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaShape 
    Type=&quot;Rectangle&quot; 
    WidthRequest=&quot;200&quot; 
    HeightRequest=&quot;100&quot;
    BackgroundColor=&quot;Blue&quot; 
    StrokeColor=&quot;White&quot; 
    StrokeWidth=&quot;2&quot; 
    CornerRadius=&quot;10&quot; /&gt;
</code></pre>
<h2 id="shape-types">Shape Types</h2>
<p>SkiaShape supports the following shape types:</p>
<ul>
<li><strong>Rectangle</strong>: A basic rectangle, optionally with rounded corners</li>
<li><strong>Circle</strong>: A perfect circle that maintains 1:1 aspect ratio</li>
<li><strong>Ellipse</strong>: An oval shape that can have different width and height</li>
<li><strong>Path</strong>: A custom shape defined by SVG path data</li>
<li><strong>Polygon</strong>: A shape defined by a collection of points</li>
<li><strong>Line</strong>: A series of connected line segments</li>
<li><strong>Arc</strong>: A circular arc segment</li>
</ul>
<h2 id="common-properties">Common Properties</h2>
<h3 id="visual-properties">Visual Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>BackgroundColor</code></td>
<td>Color</td>
<td>Fill color of the shape</td>
</tr>
<tr>
<td><code>StrokeColor</code></td>
<td>Color</td>
<td>Outline color of the shape</td>
</tr>
<tr>
<td><code>StrokeWidth</code></td>
<td>float</td>
<td>Width of the outline stroke</td>
</tr>
<tr>
<td><code>CornerRadius</code></td>
<td>float</td>
<td>Rounded corner radius for rectangles</td>
</tr>
<tr>
<td><code>StrokeCap</code></td>
<td>StrokeCap</td>
<td>End cap style for lines (Round, Butt, Square)</td>
</tr>
<tr>
<td><code>StrokePath</code></td>
<td>string</td>
<td>Dash pattern for creating dashed lines</td>
</tr>
<tr>
<td><code>StrokeBlendMode</code></td>
<td>BlendMode</td>
<td>Controls how strokes blend with underlying content</td>
</tr>
<tr>
<td><code>ClipBackgroundColor</code></td>
<td>bool</td>
<td>If true, creates a &quot;hollow&quot; shape with just shadows and strokes</td>
</tr>
</tbody>
</table>
<h3 id="shape-specific-properties">Shape-Specific Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>PathData</code></td>
<td>string</td>
<td>SVG path data for Path type shapes</td>
</tr>
<tr>
<td><code>Points</code></td>
<td>Collection&lt;SkiaPoint&gt;</td>
<td>Collection of points for Polygon or Line shapes</td>
</tr>
<tr>
<td><code>SmoothPoints</code></td>
<td>float</td>
<td>Level of smoothing for Polygon/Line shapes (0.0-1.0)</td>
</tr>
<tr>
<td><code>StartAngle</code></td>
<td>float</td>
<td>Starting angle for Arc shapes</td>
</tr>
<tr>
<td><code>SweepAngle</code></td>
<td>float</td>
<td>Sweep angle for Arc shapes</td>
</tr>
</tbody>
</table>
<h2 id="advanced-features">Advanced Features</h2>
<h3 id="shadow-effects">Shadow Effects</h3>
<p>SkiaShape supports multiple shadows through the <code>Shadows</code> collection property:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaShape 
    Type=&quot;Rectangle&quot; 
    BackgroundColor=&quot;White&quot; 
    CornerRadius=&quot;20&quot;&gt;
    &lt;DrawUi:SkiaShape.Shadows&gt;
        &lt;DrawUi:SkiaShadow 
            Color=&quot;#80000000&quot; 
            BlurRadius=&quot;10&quot; 
            Offset=&quot;0,4&quot; /&gt;
    &lt;/DrawUi:SkiaShape.Shadows&gt;
&lt;/DrawUi:SkiaShape&gt;
</code></pre>
<h3 id="gradients">Gradients</h3>
<p>SkiaShape supports gradient fills via the <code>BackgroundGradient</code> and <code>StrokeGradient</code> properties:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaShape Type=&quot;Rectangle&quot;&gt;
    &lt;DrawUi:SkiaShape.BackgroundGradient&gt;
        &lt;DrawUi:SkiaGradient 
            Type=&quot;Linear&quot; 
            StartColor=&quot;Red&quot; 
            EndColor=&quot;Blue&quot; 
            StartPoint=&quot;0,0&quot; 
            EndPoint=&quot;1,1&quot; /&gt;
    &lt;/DrawUi:SkiaShape.BackgroundGradient&gt;
&lt;/DrawUi:SkiaShape&gt;
</code></pre>
<h3 id="custom-paths">Custom Paths</h3>
<p>For complex shapes, you can use SVG path data:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaShape 
    Type=&quot;Path&quot; 
    PathData=&quot;M0,0L15.825,8.0 31.65,15.99 15.82,23.99 0,32 0,15.99z&quot; 
    BackgroundColor=&quot;Red&quot; /&gt;
</code></pre>
<p>The PathData property follows standard SVG path notation:</p>
<ul>
<li>M: Move to (absolute)</li>
<li>m: Move to (relative)</li>
<li>L: Line to (absolute)</li>
<li>l: Line to (relative)</li>
<li>H/h: Horizontal line</li>
<li>V/v: Vertical line</li>
<li>C/c: Cubic bezier curve</li>
<li>S/s: Smooth cubic bezier</li>
<li>Q/q: Quadratic bezier curve</li>
<li>T/t: Smooth quadratic bezier</li>
<li>A/a: Elliptical arc</li>
<li>Z/z: Close path</li>
</ul>
<h3 id="as-a-content-container">As a Content Container</h3>
<p>SkiaShape can function as a container, clipping child elements to its shape boundaries:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaShape 
    Type=&quot;Circle&quot; 
    BackgroundColor=&quot;Green&quot; 
    WidthRequest=&quot;200&quot; 
    HeightRequest=&quot;200&quot;&gt;
    &lt;DrawUi:SkiaImage 
        Source=&quot;background.jpg&quot; 
        VerticalOptions=&quot;Fill&quot; 
        HorizontalOptions=&quot;Fill&quot; /&gt;
    &lt;DrawUi:SkiaLabel 
        Text=&quot;Circular Content&quot; 
        TextColor=&quot;White&quot; 
        HorizontalOptions=&quot;Center&quot; 
        VerticalOptions=&quot;Center&quot; /&gt;
&lt;/DrawUi:SkiaShape&gt;
</code></pre>
<p>The <code>LayoutChildren</code> property controls how children are arranged (Absolute, Column, Row, Grid).</p>
<h2 id="creating-polygons">Creating Polygons</h2>
<p>For polygon shapes, you can define points in various ways:</p>
<h3 id="using-skiapoint-collection">Using SkiaPoint Collection</h3>
<pre><code class="lang-xml">&lt;DrawUi:SkiaShape 
    Type=&quot;Polygon&quot; 
    BackgroundColor=&quot;Purple&quot;&gt;
    &lt;DrawUi:SkiaShape.Points&gt;
        &lt;DrawUi:SkiaPoint X=&quot;0&quot; Y=&quot;0&quot; /&gt;
        &lt;DrawUi:SkiaPoint X=&quot;100&quot; Y=&quot;0&quot; /&gt;
        &lt;DrawUi:SkiaPoint X=&quot;100&quot; Y=&quot;100&quot; /&gt;
        &lt;DrawUi:SkiaPoint X=&quot;0&quot; Y=&quot;100&quot; /&gt;
    &lt;/DrawUi:SkiaShape.Points&gt;
&lt;/DrawUi:SkiaShape&gt;
</code></pre>
<h3 id="using-relative-coordinates">Using Relative Coordinates</h3>
<p>You can define points using relative coordinates (0.0-1.0) that automatically scale to the shape's dimensions:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaShape 
    Type=&quot;Polygon&quot; 
    BackgroundColor=&quot;CornflowerBlue&quot;&gt;
    &lt;DrawUi:SkiaShape.Points&gt;
        &lt;DrawUi:SkiaPoint X=&quot;0.0&quot; Y=&quot;0.8&quot; /&gt;
        &lt;DrawUi:SkiaPoint X=&quot;0.0&quot; Y=&quot;0.7&quot; /&gt;
        &lt;DrawUi:SkiaPoint X=&quot;1.0&quot; Y=&quot;0.2&quot; /&gt;
        &lt;DrawUi:SkiaPoint X=&quot;1.0&quot; Y=&quot;0.3&quot; /&gt;
    &lt;/DrawUi:SkiaShape.Points&gt;
&lt;/DrawUi:SkiaShape&gt;
</code></pre>
<h3 id="using-string-definition">Using String Definition</h3>
<p>You can also use a converter for inline point definitions:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaShape 
    Type=&quot;Polygon&quot; 
    BackgroundColor=&quot;Purple&quot;
    Points=&quot;0,0 100,0 100,100 0,100&quot; /&gt;
</code></pre>
<h3 id="predefined-shapes">Predefined Shapes</h3>
<p>SkiaShape provides predefined point collections for common shapes:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaShape 
    Type=&quot;Polygon&quot; 
    BackgroundColor=&quot;Yellow&quot;
    Points=&quot;{x:Static DrawUi:SkiaShape.PolygonStar}&quot; /&gt;
</code></pre>
<h3 id="smooth-curves">Smooth Curves</h3>
<p>For smoother, curved polygons, adjust the <code>SmoothPoints</code> property (0.0-1.0):</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaShape 
    Type=&quot;Polygon&quot; 
    BackgroundColor=&quot;#220000FF&quot; 
    SmoothPoints=&quot;0.9&quot;
    Points=&quot;0.0,0.8 0.0,0.7 1.0,0.2 1.0,0.3&quot; /&gt;
</code></pre>
<p>A value of 0 creates sharp corners, while a value of 1.0 creates maximally smooth curves.</p>
<h2 id="creating-lines">Creating Lines</h2>
<p>Lines can be created using the same point collection approach:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaShape 
    Type=&quot;Line&quot; 
    StrokeColor=&quot;Black&quot; 
    StrokeWidth=&quot;2&quot;
    Points=&quot;0,0 50,50 100,0 150,50&quot; /&gt;
</code></pre>
<p>Customize line appearance with:</p>
<ul>
<li><code>StrokeCap</code>: Controls how line ends appear</li>
<li><code>StrokePath</code>: Define dash patterns (&quot;5,5&quot; creates 5px dashes with 5px gaps)</li>
</ul>
<h2 id="practical-examples">Practical Examples</h2>
<h3 id="card-with-shadow">Card with Shadow</h3>
<pre><code class="lang-xml">&lt;DrawUi:SkiaShape 
    Type=&quot;Rectangle&quot; 
    BackgroundColor=&quot;White&quot; 
    CornerRadius=&quot;12&quot; 
    Padding=&quot;16&quot;
    WidthRequest=&quot;300&quot; 
    HeightRequest=&quot;150&quot;
    LayoutChildren=&quot;Column&quot;&gt;
    
    &lt;DrawUi:SkiaShape.Shadows&gt;
        &lt;DrawUi:SkiaShadow 
            Color=&quot;#22000000&quot; 
            BlurRadius=&quot;20&quot; 
            Offset=&quot;0,4&quot; /&gt;
    &lt;/DrawUi:SkiaShape.Shadows&gt;
    
    &lt;DrawUi:SkiaLabel 
        Text=&quot;Card Title&quot; 
        FontSize=&quot;18&quot; 
        FontWeight=&quot;Bold&quot; /&gt;
    
    &lt;DrawUi:SkiaLabel 
        Text=&quot;This is a card with rounded corners and a shadow effect. SkiaShape makes it easy to create modern UI components.&quot; 
        TextColor=&quot;#666666&quot; 
        Margin=&quot;0,10,0,0&quot; /&gt;
&lt;/DrawUi:SkiaShape&gt;
</code></pre>
<h3 id="progress-indicator">Progress Indicator</h3>
<pre><code class="lang-xml">&lt;DrawUi:SkiaShape 
    Type=&quot;Arc&quot; 
    StrokeColor=&quot;#EEEEEE&quot; 
    StrokeWidth=&quot;10&quot; 
    BackgroundColor=&quot;Transparent&quot;
    StartAngle=&quot;0&quot; 
    SweepAngle=&quot;360&quot; 
    WidthRequest=&quot;100&quot; 
    HeightRequest=&quot;100&quot;&gt;
    
    &lt;DrawUi:SkiaShape 
        Type=&quot;Arc&quot; 
        StrokeColor=&quot;Blue&quot; 
        StrokeWidth=&quot;10&quot; 
        BackgroundColor=&quot;Transparent&quot;
        StartAngle=&quot;0&quot; 
        SweepAngle=&quot;{Binding Progress}&quot; 
        WidthRequest=&quot;100&quot; 
        HeightRequest=&quot;100&quot; /&gt;
    
    &lt;DrawUi:SkiaLabel 
        Text=&quot;{Binding ProgressText}&quot; 
        HorizontalOptions=&quot;Center&quot; 
        VerticalOptions=&quot;Center&quot; /&gt;
&lt;/DrawUi:SkiaShape&gt;
</code></pre>
<h3 id="custom-button">Custom Button</h3>
<pre><code class="lang-xml">&lt;DrawUi:SkiaShape 
    Type=&quot;Path&quot; 
    PathData=&quot;M10,0 L90,0 C95,0 100,5 100,10 L100,40 C100,45 95,50 90,50 L10,50 C5,50 0,45 0,40 L0,10 C0,5 5,0 10,0 Z&quot; 
    BackgroundColor=&quot;Blue&quot; 
    WidthRequest=&quot;100&quot; 
    HeightRequest=&quot;50&quot;&gt;
    
    &lt;DrawUi:SkiaShape.GestureRecognizers&gt;
        &lt;TapGestureRecognizer Command=&quot;{Binding ButtonCommand}&quot; /&gt;
    &lt;/DrawUi:SkiaShape.GestureRecognizers&gt;
    
    &lt;DrawUi:SkiaLabel 
        Text=&quot;SUBMIT&quot; 
        TextColor=&quot;White&quot; 
        FontWeight=&quot;Bold&quot;
        HorizontalOptions=&quot;Center&quot; 
        VerticalOptions=&quot;Center&quot; /&gt;
&lt;/DrawUi:SkiaShape&gt;
</code></pre>
<h2 id="performance-considerations">Performance Considerations</h2>
<ul>
<li>For static shapes, set <code>Cache=&quot;Image&quot;</code> to render once and cache as bitmap</li>
<li>For frequently animated shapes, use <code>Cache=&quot;Operations&quot;</code> for best performance</li>
<li>Avoid excessive shadows or complex paths in performance-critical UI</li>
<li>For very complex paths, pre-process SVG data when possible rather than computing at runtime</li>
</ul>
<h2 id="platform-specific-notes">Platform Specific Notes</h2>
<p>SkiaShape renders consistently across all platforms supported by MAUI, ensuring that your UI maintains the same appearance on Android, iOS, Windows, and macOS.</p>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/shapes.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../../styles/docfx.js"></script>
    <script type="text/javascript" src="../../styles/main.js"></script>
  </body>
</html>
