<?xml version="1.0" encoding="UTF-8"?>
<module external.linked.project.id=":camera" external.linked.project.path="$MODULE_DIR$/../../../camera" external.root.project.path="$MODULE_DIR$/../../.." external.system.id="GRADLE" external.system.module.group="AppoMobi NativeTools" external.system.module.version="unspecified" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="android-gradle" name="Android-Gradle">
      <configuration>
        <option name="GRADLE_PROJECT_PATH" value=":camera" />
        <option name="LAST_SUCCESSFUL_SYNC_AGP_VERSION" value="4.0.0" />
        <option name="LAST_KNOWN_AGP_VERSION" value="4.0.0" />
      </configuration>
    </facet>
    <facet type="android" name="Android">
      <configuration>
        <option name="SELECTED_BUILD_VARIANT" value="debug" />
        <option name="ASSEMBLE_TASK_NAME" value="assembleDebug" />
        <option name="COMPILE_JAVA_TASK_NAME" value="compileDebugSources" />
        <afterSyncTasks>
          <task>generateDebugSources</task>
        </afterSyncTasks>
        <option name="ALLOW_USER_CONFIGURATION" value="false" />
        <option name="MANIFEST_FILE_RELATIVE_PATH" value="/src/main/AndroidManifest.xml" />
        <option name="RES_FOLDER_RELATIVE_PATH" value="/src/main/res" />
        <option name="RES_FOLDERS_RELATIVE_PATH" value="file://$MODULE_DIR$/../../../camera/src/main/res;file://$MODULE_DIR$/../../../camera/src/debug/res;file://$MODULE_DIR$/../../../camera/build/generated/res/rs/debug" />
        <option name="TEST_RES_FOLDERS_RELATIVE_PATH" value="file://$MODULE_DIR$/../../../camera/src/androidTest/res;file://$MODULE_DIR$/../../../camera/src/androidTestDebug/res;file://$MODULE_DIR$/../../../camera/build/generated/res/rs/androidTest/debug" />
        <option name="ASSETS_FOLDER_RELATIVE_PATH" value="/src/main/assets" />
        <option name="PROJECT_TYPE" value="1" />
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_7">
    <output url="file://$MODULE_DIR$/../../../camera/build/intermediates/javac/debug/classes" />
    <output-test url="file://$MODULE_DIR$/../../../camera/build/intermediates/javac/debugUnitTest/classes" />
    <exclude-output />
    <content url="file://$MODULE_DIR$/../../../camera">
      <excludeFolder url="file://$MODULE_DIR$/../../../camera/.gradle" />
      <excludeFolder url="file://$MODULE_DIR$/../../../camera/build" />
    </content>
    <orderEntry type="jdk" jdkName="Android API 33, extension level 3 Platform" jdkType="Android SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>