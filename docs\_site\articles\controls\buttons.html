<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Button Controls | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Button Controls | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../../styles/docfx.css">
      <link rel="stylesheet" href="../../styles/main.css">
      <meta property="docfx:navrel" content="../../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../../index.html">
                <img id="logo" class="svg" src="../../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="button-controls">Button Controls</h1>

<p>DrawnUi provides highly customizable button controls with platform-specific styling and support for custom content.</p>
<h2 id="skiabutton">SkiaButton</h2>
<p><code>SkiaButton</code> is a versatile button control supporting different button styles, platform-specific appearance, and custom content. You can use the default content or provide your own child views. If you use conventional tags (<code>BtnText</code>, <code>BtnShape</code>), SkiaButton will apply its properties (like <code>Text</code>, <code>TextColor</code>, etc.) to those views automatically.</p>
<blockquote>
<p><strong>Note:</strong> If you provide custom content, use the tags <code>BtnText</code> for your main label and <code>BtnShape</code> for the button background to enable property binding.</p>
</blockquote>
<h3 id="basic-usage">Basic Usage</h3>
<pre><code class="lang-xml">&lt;draw:SkiaButton
    Text=&quot;Click Me&quot;
    WidthRequest=&quot;120&quot;
    HeightRequest=&quot;40&quot;
    BackgroundColor=&quot;Blue&quot;
    TextColor=&quot;White&quot;
    CornerRadius=&quot;8&quot;
    Clicked=&quot;OnButtonClicked&quot; /&gt;
</code></pre>
<h3 id="custom-content-example">Custom Content Example</h3>
<pre><code class="lang-xml">&lt;draw:SkiaButton&gt;
    &lt;draw:SkiaShape Tag=&quot;BtnShape&quot; BackgroundColor=&quot;Red&quot; CornerRadius=&quot;12&quot; /&gt;
    &lt;draw:SkiaLabel Tag=&quot;BtnText&quot; Text=&quot;Custom&quot; TextColor=&quot;Yellow&quot; /&gt;
&lt;/draw:SkiaButton&gt;
</code></pre>
<h3 id="button-style-types">Button Style Types</h3>
<p>SkiaButton supports multiple style variants through the <code>ButtonStyle</code> property:</p>
<ul>
<li><code>Contained</code>: Standard filled button with background color (default)</li>
<li><code>Outlined</code>: Button with outline border and transparent background</li>
<li><code>Text</code>: Button with no background or border, only text</li>
</ul>
<pre><code class="lang-xml">&lt;draw:SkiaButton
    Text=&quot;Outlined Button&quot;
    ButtonStyle=&quot;Outlined&quot;
    BackgroundColor=&quot;Blue&quot;
    TextColor=&quot;Blue&quot; /&gt;
</code></pre>
<h3 id="platform-specific-styling">Platform-Specific Styling</h3>
<p>Platform-specific styles are selected automatically or can be set in code via the <code>UsingControlStyle</code> property (not bindable in XAML). Styles include:</p>
<ul>
<li><code>Cupertino</code>: iOS-style button</li>
<li><code>Material</code>: Android Material Design button</li>
<li><code>Windows</code>: Windows-style button</li>
</ul>
<blockquote>
<p><strong>Note:</strong> There is no <code>ControlStyle</code> bindable property. Platform style is set internally or in code.</p>
</blockquote>
<h3 id="elevation">Elevation</h3>
<p>Buttons can have elevation (shadow) effects:</p>
<pre><code class="lang-xml">&lt;draw:SkiaButton
    Text=&quot;Elevated Button&quot;
    ElevationEnabled=&quot;True&quot; /&gt;
</code></pre>
<h3 id="properties">Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Text</code></td>
<td>string</td>
<td>The text displayed on the button</td>
</tr>
<tr>
<td><code>TextColor</code></td>
<td>Color</td>
<td>The color of the button text</td>
</tr>
<tr>
<td><code>BackgroundColor</code></td>
<td>Color</td>
<td>The background color of the button</td>
</tr>
<tr>
<td><code>CornerRadius</code></td>
<td>float</td>
<td>The corner radius of the button (applied via <code>BtnShape</code>)</td>
</tr>
<tr>
<td><code>ButtonStyle</code></td>
<td>ButtonStyleType</td>
<td>The button style (Contained, Outlined, Text)</td>
</tr>
<tr>
<td><code>ElevationEnabled</code></td>
<td>bool</td>
<td>Whether the button has a shadow effect</td>
</tr>
<tr>
<td><code>TextCase</code></td>
<td>TextTransform</td>
<td>The text case transformation (None, Uppercase, Lowercase)</td>
</tr>
<tr>
<td><code>FontSize</code></td>
<td>double</td>
<td>The font size of the button text</td>
</tr>
<tr>
<td><code>FontFamily</code></td>
<td>string</td>
<td>The font family of the button text</td>
</tr>
<tr>
<td><code>IsDisabled</code></td>
<td>bool</td>
<td>Disables the button if true</td>
</tr>
<tr>
<td><code>IsPressed</code></td>
<td>bool</td>
<td>True while the button is pressed</td>
</tr>
<tr>
<td><code>IconPosition</code></td>
<td>IconPositionType</td>
<td>Position of icon (icon support planned)</td>
</tr>
<tr>
<td><code>ApplyEffect</code></td>
<td>SkiaTouchAnimation</td>
<td>Touch animation effect (Ripple, Shimmer, etc.)</td>
</tr>
</tbody>
</table>
<h3 id="events">Events</h3>
<ul>
<li><code>Clicked</code>: Raised when the button is clicked/tapped</li>
<li><code>Pressed</code>: Raised when the button is pressed down</li>
<li><code>Released</code>: Raised when the button is released</li>
<li><code>Up</code>, <code>Down</code>, <code>Tapped</code>: Additional gesture events</li>
</ul>
<h3 id="icon-support">Icon Support</h3>
<p>Icon support is planned. The <code>IconPosition</code> property exists, but icon rendering is not yet implemented.</p>
<hr>
<h2 id="api-xml-documentation">API XML Documentation</h2>
<blockquote>
<p>The following methods in SkiaButton have been updated with XML documentation in the codebase:</p>
<ul>
<li><code>OnDown</code>, <code>OnUp</code>, <code>OnTapped</code>, <code>ApplyProperties</code>, <code>CreateDefaultContent</code>, <code>CreateCupertinoStyleContent</code>, <code>CreateMaterialStyleContent</code>, <code>CreateWindowsStyleContent</code>, <code>OnButtonPropertyChanged</code>, <code>FindViews</code>, <code>CreateClip</code>.</li>
</ul>
</blockquote>
<p>For more details, see the source code in <code>src/Engine/Maui/Controls/Button/SkiaButton.cs</code>.</p>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/buttons.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../../styles/docfx.js"></script>
    <script type="text/javascript" src="../../styles/main.js"></script>
  </body>
</html>
