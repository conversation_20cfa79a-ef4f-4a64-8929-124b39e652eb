
#pragma version(1)
#pragma rs java_package_name(com.appomobi.nativetools.graphics)
#pragma rs_fp_relaxed



typedef struct __attribute__((packed)) ScaleSurfaceParams {
    int height, width, mRotatedContentWidth, mRotatedContentHeight;
    bool fuulScreen;
} ScaleSurfaceParams_t;

ScaleSurfaceParams_t *scaleParams;

typedef struct __attribute__((packed)) ScaleSurfaceResult {
    int scaleX, scaleY;
} ScaleSurfaceResult_t;

ScaleSurfaceResult_t *scaleResult;





//------------------------------------------------------------------------
void init()
// -----------------------------------------------------------------------
{
	//rsDebug("Renderscript init Called ", rsUptimeMillis());
}


// -----------------------------------------------------------------------
int root()
// -----------------------------------------------------------------------
{

	//rsDebug("Renderscript root Called ", rsUptimeMillis());
    return 1;
}
