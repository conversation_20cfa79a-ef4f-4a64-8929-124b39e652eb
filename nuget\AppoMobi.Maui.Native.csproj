<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net7.0-android;</TargetFrameworks>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>

		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
	</PropertyGroup>

  <PropertyGroup>
    <Packable>True</Packable>
    <Version>*******-pre</Version>
    <Description>.Net Maui bindings for native helpers, actually android only</Description>
    <PackageTags>android maui</PackageTags>
    <PackageLicenseExpression>MIT</PackageLicenseExpression>
    <LangVersion>Latest</LangVersion>
    <RepositoryType>git</RepositoryType>
    <Title>AppoMobi.Maui.Native</Title>
    <Authors><PERSON> aka AppoMobi</Authors>
    <Copyright>(c) AppoMobi, 2023 - present day</Copyright>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\android\Bindings\AppoMobi.Maui.Native.Droid.csproj" >
      <PrivateAssets>all</PrivateAssets>
    </ProjectReference>
  </ItemGroup>

  <ItemGroup>
    <None Include="bin\Release\net7.0-android\AppoMobi.Maui.Native.Droid.dll">
      <Pack>true</Pack>
      <PackagePath>lib\net7.0-android33.0\</PackagePath>
    </None>
  </ItemGroup>
</Project>
