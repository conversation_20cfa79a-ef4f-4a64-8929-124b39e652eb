<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>DrawnUi Documentation | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="DrawnUi Documentation | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="images/favicon.ico">
      <link rel="stylesheet" href="styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="styles/docfx.css">
      <link rel="stylesheet" href="styles/main.css">
      <meta property="docfx:navrel" content="toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="index.html">
                <img id="logo" class="svg" src="images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">
        <div class="article row grid">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="drawnui-documentation">DrawnUi Documentation</h1>

<p><strong>NOTE: this is under heavy construction AND NOT READY TO USE YET, may contain some outdated or non-exact information!!!.</strong></p>
<p>Rendering engine to draw your UI on a Skia canvas, with gestures and animations, designed to draw pixel-perfect custom controls instead of using native ones, powered by <a href="https://github.com/mono/SkiaSharp">SkiaSharp</a>😍.
Create and render your custom controls on a hardware-accelerated Skia canvas with an improved common MAUI layout system.</p>
<p>Supports <strong>iOS</strong>, <strong>MacCatalyst</strong>, <strong>Android</strong>, <strong>Windows</strong>.</p>
<ul>
<li>To use inside a usual MAUI app, consume drawn controls here and there inside <code>Canvas</code> views.</li>
<li>Create a totally drawn app with just one <code>Canvas</code> as root view, <code>SkiaShell</code> is provided for navigation.</li>
<li>Drawn controls are totally virtual, these are commands for the engine on what and how to draw on a skia canvas.</li>
<li>Free to use under the MIT license, a nuget package is available.</li>
</ul>
<h2 id="about">About</h2>
<p><a href="https://taublast.github.io/posts/MauiJuly/">A small article</a> about the library and why it was created</p>
<h2 id="demo-apps">Demo Apps</h2>
<ul>
<li>This repo includes a Sandbox project for some custom controls, with playground examples, custom controls, maps etc</li>
<li>More creating custom controls examples inside the <a href="https://github.com/taublast/AppoMobi.Maui.DrawnUi.Demo">Engine Demo</a> 🤩 <strong>Updated with latest nuget!</strong></li>
<li>A <a href="https://github.com/taublast/AppoMobi.Maui.DrawnUi.SpaceShooter">dynamic arcade game</a> drawn with this engine, uses preview nuget with SkiaSharp v3.</li>
<li>A <a href="https://github.com/taublast/SurfAppCompareDrawn">drawn CollectionView demo</a> where you could see how simple and profitable it is to convert an existing recycled cells list into a drawn one</li>
<li><a href="https://github.com/taublast/ShadersCarousel/">Shaders Carousel Demo</a> featuring SkiaSharp v3 capabilities</li>
</ul>
<h2 id="features">Features</h2>
<ul>
<li><strong>SkiaSharp Rendering</strong>: All controls are rendered using SkiaSharp for maximum performance</li>
<li><strong>Platform Styling</strong>: Automatic styling based on the current platform (iOS, Android, Windows)</li>
<li><strong>Rich Controls</strong>: Buttons, switches, checkboxes, and more with full styling support</li>
<li><strong>Animation</strong>: Built-in animation capabilities for rich, interactive UIs</li>
<li><strong>Customization</strong>: Extensive customization options for all controls</li>
</ul>
<h2 id="documentation-structure">Documentation Structure</h2>
<p>This documentation is organized into the following sections:</p>
<ul>
<li><a href="articles/getting-started.html">Getting Started</a>: Quick start guide and installation</li>
<li><a href="articles/controls/index.html">Controls</a>: Detailed documentation for each control</li>
<li><a href="api/index.html">API Reference</a>: Complete API documentation</li>
<li><a href="articles/samples.html">Samples</a>: Example applications and code snippets</li>
</ul>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/index.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="styles/docfx.js"></script>
    <script type="text/javascript" src="styles/main.js"></script>
  </body>
</html>
