﻿using Microsoft.Maui.Handlers;
using UIKit;

namespace DrawnUi.Views
{
    /// <summary>
    /// APPLE Handler with Mapper (originally iOS)
    /// </summary>
    public partial class SKGLViewHandlerEnhanced : ViewHandler<ISKGLView, SKMetalViewEnhanced>
    {
        private PaintSurfaceProxy? paintSurfaceProxy;

        protected override SKMetalViewEnhanced CreatePlatformView() =>
            new MauiSkMetalViewEnhanced
            {
                BackgroundColor = UIColor.Clear,
                Opaque = false,
            };


        protected override void ConnectHandler(SKMetalViewEnhanced platformView)
        {
            paintSurfaceProxy = new();
            paintSurfaceProxy.Connect(VirtualView, platformView);

            base.ConnectHandler(platformView);
        }

        protected override void DisconnectHandler(SKMetalViewEnhanced platformView)
        {
            paintSurfaceProxy?.Disconnect(platformView);
            paintSurfaceProxy = null;

            base.DisconnectHandler(platformView);
        }

        // Mapper actions / properties

        public static void OnInvalidateSurface(SKGLViewHandlerEnhanced handler, ISKGLView view, object? args)
        {
            if (handler.PlatformView.ManualRefresh)
                handler.PlatformView.SetNeedsDisplay();
        }

        public static void MapIgnorePixelScaling(SKGLViewHandlerEnhanced handler, ISKGLView view)
        {
            if (handler.PlatformView is MauiSkMetalViewEnhanced pv)
            {
                pv.IgnorePixelScaling = view.IgnorePixelScaling;
                handler.PlatformView.SetNeedsDisplay();
            }
        }

        public static void MapHasRenderLoop(SKGLViewHandlerEnhanced handler, ISKGLView view)
        {
            handler.PlatformView.Paused = !view.HasRenderLoop;
            handler.PlatformView.EnableSetNeedsDisplay = !view.HasRenderLoop;
        }

        public static void MapEnableTouchEvents(SKGLViewHandlerEnhanced handler, ISKGLView view)
        {
           //not used
        }

        // helper methods

        private class MauiSkMetalViewEnhanced : SKMetalViewEnhanced
        {
            public bool IgnorePixelScaling { get; set; }

            protected override void OnPaintSurface(SkiaSharp.Views.iOS.SKPaintMetalSurfaceEventArgs e)
            {
                if (IgnorePixelScaling)
                {
                    var userVisibleSize = new SKSizeI((int)Bounds.Width, (int)Bounds.Height);
                    var canvas = e.Surface.Canvas;
                    canvas.Scale((float)ContentScaleFactor);
                    canvas.Save();

                    e = new SkiaSharp.Views.iOS.SKPaintMetalSurfaceEventArgs(e.Surface, e.BackendRenderTarget, e.Origin, e.Info.WithSize(userVisibleSize), e.Info);
                }

                base.OnPaintSurface(e);
            }
        }

        private class PaintSurfaceProxy : SKEventProxy<ISKGLView, SKMetalViewEnhanced>
        {
            private SKSizeI lastCanvasSize;
            private GRContext? lastGRContext;

            protected override void OnConnect(ISKGLView virtualView, SKMetalViewEnhanced platformView) =>
                platformView.PaintSurface += OnPaintSurface;

            protected override void OnDisconnect(SKMetalViewEnhanced platformView) =>
                platformView.PaintSurface -= OnPaintSurface;

            private void OnPaintSurface(object? sender, SkiaSharp.Views.iOS.SKPaintMetalSurfaceEventArgs e)
            {
                if (VirtualView is not { } view)
                    return;

                var newCanvasSize = e.Info.Size;
                if (lastCanvasSize != newCanvasSize)
                {
                    lastCanvasSize = newCanvasSize;
                    view.OnCanvasSizeChanged(newCanvasSize);
                }
                if (sender is SKMetalViewEnhanced platformView)
                {
                    var newGRContext = platformView.GRContext;
                    if (lastGRContext != newGRContext)
                    {
                        lastGRContext = newGRContext;
                        view.OnGRContextChanged(newGRContext);
                    }
                }

                view.OnPaintSurface(new (e.Surface, e.BackendRenderTarget, e.Origin, e.Info, e.RawInfo));
            }
        }



    }
}
