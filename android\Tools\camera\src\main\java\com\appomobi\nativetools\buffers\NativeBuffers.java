package com.appomobi.nativetools.buffers;

import android.app.Activity;
import android.content.res.AssetFileDescriptor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.renderscript.RenderScript;
import android.util.Log;
import android.content.Context;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;

public final class NativeBuffers {

    public static MappedByteBuffer GetAssetsFileAsByteBuffer(Context context, String modelPath)
    {
        try {
            AssetFileDescriptor fileDescriptor = context.getAssets().openFd(modelPath);
            FileInputStream inputStream = new FileInputStream(fileDescriptor.getFileDescriptor());
            FileChannel fileChannel = inputStream.getChannel();
            long startOffset = fileDescriptor.getStartOffset();
            long declaredLength = fileDescriptor.getDeclaredLength();
            return fileChannel.map(FileChannel.MapMode.READ_ONLY, startOffset, declaredLength);
        } catch (IOException e) {
            Log.d("GetFileAsByteBuffer", e.toString());
            return null;
        }
    }

    public static MappedByteBuffer GetFileAsByteBuffer(String modelPath) {
        try {
            FileInputStream inputStream = new FileInputStream(modelPath);
            FileChannel fileChannel = inputStream.getChannel();
            long startOffset = 0L;
            long declaredLength = fileChannel.size();
            return fileChannel.map(FileChannel.MapMode.READ_ONLY, startOffset, declaredLength);
        } catch (IOException e) {
            Log.e("GetFileAsByteBuffer", "IOException occurred", e);
            return null;
        }
    }

    //Convert resizedBitmap [RGBA] to ByteBuffer (native order) filled with [RGB] floats
    public static ByteBuffer GetRrbByteBuffer(Bitmap resizedBitmap) {

        // Prepare ByteBuffer
        int bytesCount = resizedBitmap.getWidth() * resizedBitmap.getHeight() * 4 * 3;  // 4 for float size, 3 for RGB channels
        ByteBuffer byteBuffer = ByteBuffer.allocateDirect(bytesCount);
        byteBuffer.order(ByteOrder.nativeOrder());

        // Fill the ByteBuffer
        int[] pixels = new int[resizedBitmap.getWidth() * resizedBitmap.getHeight()];
        resizedBitmap.getPixels(pixels, 0, resizedBitmap.getWidth(), 0, 0, resizedBitmap.getWidth(), resizedBitmap.getHeight());

        for (int pixel : pixels) {
            byteBuffer.putFloat(((pixel >> 16) & 0xFF) / 255.0f);
            byteBuffer.putFloat(((pixel >> 8) & 0xFF) / 255.0f);
            byteBuffer.putFloat((pixel & 0xFF) / 255.0f);
        }

        resizedBitmap.recycle();

        byteBuffer.rewind();
        return byteBuffer;
    }

    public static ByteBuffer GetPhotoAsRgbByteBuffer(byte[] bytes, int targetWidth, int targetHeight, float cropAspectRatio)
    {
        if (bytes == null || bytes.length == 0)
        {
            return null;
        }

        Bitmap resizedBitmap = СropAndResizeBitmap(bytes, targetWidth, targetHeight, cropAspectRatio);

        return GetRrbByteBuffer(resizedBitmap);
    }

    public static ByteBuffer GetPhotoAsRgbByteBuffer(byte[] bytes, int targetWidth, int targetHeight) {

        if (bytes == null || bytes.length == 0) {
            return null;
        }

        // Decode bitmap from byte array
        Bitmap originalBitmap = BitmapFactory.decodeByteArray(bytes, 0, bytes.length);

        if (originalBitmap == null) {
            return null;
        }

        // Crop and scale image
        int dimension = Math.min(originalBitmap.getWidth(), originalBitmap.getHeight());
        int x = (originalBitmap.getWidth() - dimension) / 2;
        int y = (originalBitmap.getHeight() - dimension) / 2;

        Bitmap croppedBitmap = Bitmap.createBitmap(originalBitmap, x, y, dimension, dimension);
        Bitmap resizedBitmap = Bitmap.createScaledBitmap(croppedBitmap, targetWidth, targetHeight, true);

        ByteBuffer buffer = GetRrbByteBuffer(resizedBitmap);

        originalBitmap.recycle();
        croppedBitmap.recycle();
        resizedBitmap.recycle();

        return buffer;
    }

    public static Bitmap СropAndResizeBitmap(byte[] bytes, int targetWidth, int targetHeight, float cropAspectRatio)
    {
        if (bytes == null || bytes.length == 0) {
            return null;
        }

        // Decode bitmap from byte array
        Bitmap originalBitmap = BitmapFactory.decodeByteArray(bytes, 0, bytes.length);

        if (originalBitmap == null) {
            return null;
        }

        // Calculate dimensions for cropping
        int originalWidth = originalBitmap.getWidth();
        int originalHeight = originalBitmap.getHeight();

        // Find the smallest side
        int smallestSide = Math.min(originalWidth, originalHeight);

        // Reduce the crop rectangle by multiplying the smallest side by cropAspectRatio
        int cropSize = Math.round(smallestSide * cropAspectRatio);

        // Calculate the starting x and y position for cropping (center the crop rectangle)
        int x = (originalWidth - cropSize) / 2;
        int y = (originalHeight - cropSize) / 2;

        // Crop the bitmap
        Bitmap croppedBitmap = Bitmap.createBitmap(originalBitmap, x, y, cropSize, cropSize);

        // Scale the bitmap to the target size
        Bitmap resizedBitmap = Bitmap.createScaledBitmap(croppedBitmap, targetWidth, targetHeight, true);

        // Recycle unused bitmaps
        originalBitmap.recycle();
        croppedBitmap.recycle();

        return resizedBitmap;
    }

}
