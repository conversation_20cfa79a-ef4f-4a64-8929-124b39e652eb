<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Scroll Controls | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Scroll Controls | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../../styles/docfx.css">
      <link rel="stylesheet" href="../../styles/main.css">
      <meta property="docfx:navrel" content="../../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../../index.html">
                <img id="logo" class="svg" src="../../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="scroll-controls">Scroll Controls</h1>

<p>DrawnUi.Maui provides powerful scrolling containers that offer high-performance scrolling with advanced features like virtualization, infinite scrolling, and pull-to-refresh. This article covers the scroll controls available in the framework.</p>
<h2 id="skiascroll">SkiaScroll</h2>
<p>SkiaScroll is the core scrolling container in DrawnUi.Maui, providing smooth scrolling capabilities with physics-based animations and gesture handling.</p>
<h3 id="basic-usage">Basic Usage</h3>
<pre><code class="lang-xml">&lt;DrawUi:SkiaScroll 
    Orientation=&quot;Vertical&quot;
    WidthRequest=&quot;400&quot;
    HeightRequest=&quot;600&quot;&gt;
    
    &lt;DrawUi:SkiaLayout LayoutType=&quot;Column&quot; Spacing=&quot;10&quot;&gt;
        &lt;DrawUi:SkiaLabel Text=&quot;Item 1&quot; /&gt;
        &lt;DrawUi:SkiaLabel Text=&quot;Item 2&quot; /&gt;
        &lt;DrawUi:SkiaLabel Text=&quot;Item 3&quot; /&gt;
        &lt;!-- More items --&gt;
    &lt;/DrawUi:SkiaLayout&gt;
    
&lt;/DrawUi:SkiaScroll&gt;
</code></pre>
<h3 id="multi-directional-scrolling">Multi-Directional Scrolling</h3>
<p>SkiaScroll supports scrolling in multiple directions:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaScroll
    Orientation=&quot;Both&quot;
    HorizontalOptions=&quot;Fill&quot;
    VerticalOptions=&quot;Fill&quot;&gt;
    
    &lt;DrawUi:SkiaLayout
        HeightRequest=&quot;1500&quot;
        WidthRequest=&quot;1500&quot;&gt;
        &lt;!-- Content larger than viewport --&gt;
        &lt;DrawUi:SkiaImage Source=&quot;large_image.jpg&quot; /&gt;
    &lt;/DrawUi:SkiaLayout&gt;
    
&lt;/DrawUi:SkiaScroll&gt;
</code></pre>
<h3 id="zoomable-content">Zoomable Content</h3>
<p>SkiaScroll supports pinch-to-zoom functionality:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaScroll
    Orientation=&quot;Both&quot;
    ZoomLocked=&quot;False&quot;
    ZoomMin=&quot;1&quot;
    ZoomMax=&quot;3&quot;&gt;
    
    &lt;DrawUi:SkiaLayout&gt;
        &lt;DrawUi:SkiaImage Source=&quot;zoomable_image.jpg&quot; /&gt;
    &lt;/DrawUi:SkiaLayout&gt;
    
&lt;/DrawUi:SkiaScroll&gt;
</code></pre>
<p>The zoom properties control the behavior:</p>
<ul>
<li><code>ZoomLocked</code>: When true, prevents zooming</li>
<li><code>ZoomMin</code>: Minimum zoom level (1.0 = original size)</li>
<li><code>ZoomMax</code>: Maximum zoom level</li>
</ul>
<h3 id="key-properties">Key Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Orientation</code></td>
<td>ScrollOrientation</td>
<td>Direction of scrolling (Vertical, Horizontal, Both)</td>
</tr>
<tr>
<td><code>Content</code></td>
<td>SkiaControl</td>
<td>The scrollable content</td>
</tr>
<tr>
<td><code>Header</code></td>
<td>SkiaControl</td>
<td>Optional header element</td>
</tr>
<tr>
<td><code>Footer</code></td>
<td>SkiaControl</td>
<td>Optional footer element</td>
</tr>
<tr>
<td><code>HeaderSticky</code></td>
<td>bool</td>
<td>If true, header remains fixed during scrolling</td>
</tr>
<tr>
<td><code>HeaderBehind</code></td>
<td>bool</td>
<td>If true, header appears behind scrollable content</td>
</tr>
<tr>
<td><code>ViewportOffsetX</code></td>
<td>float</td>
<td>Horizontal scroll position</td>
</tr>
<tr>
<td><code>ViewportOffsetY</code></td>
<td>float</td>
<td>Vertical scroll position</td>
</tr>
<tr>
<td><code>UseVirtual</code></td>
<td>bool</td>
<td>Enables virtualization for large content</td>
</tr>
<tr>
<td><code>ScrollWidthRequest</code></td>
<td>float</td>
<td>Width of the scrollable area</td>
</tr>
<tr>
<td><code>ScrollHeightRequest</code></td>
<td>float</td>
<td>Height of the scrollable area</td>
</tr>
<tr>
<td><code>EnableScrolling</code></td>
<td>bool</td>
<td>Enables/disables scrolling</td>
</tr>
</tbody>
</table>
<h3 id="scrolling-behavior-properties">Scrolling Behavior Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>EnableMouseWheel</code></td>
<td>bool</td>
<td>Controls mouse wheel scrolling</td>
</tr>
<tr>
<td><code>ScrollVelocityThreshold</code></td>
<td>float</td>
<td>Velocity threshold for scrolling</td>
</tr>
<tr>
<td><code>ThresholdSwipeOnUp</code></td>
<td>float</td>
<td>Minimum velocity for fling animation</td>
</tr>
<tr>
<td><code>SystemAnimationTimeSecs</code></td>
<td>float</td>
<td>Duration for system animations</td>
</tr>
<tr>
<td><code>ParallaxOverscrollEnabled</code></td>
<td>bool</td>
<td>Enables parallax effect when overscrolling</td>
</tr>
<tr>
<td><code>OverscrollEnabled</code></td>
<td>bool</td>
<td>Enables overscroll bouncing effect</td>
</tr>
<tr>
<td><code>HeaderParallaxRatio</code></td>
<td>float</td>
<td>Controls parallax effect for header</td>
</tr>
<tr>
<td><code>ScrollPositionParallaxRatio</code></td>
<td>float</td>
<td>Controls parallax effect for content</td>
</tr>
<tr>
<td><code>CurrentSmoothScrollY</code></td>
<td>float</td>
<td>Current smooth scroll position (vertical)</td>
</tr>
<tr>
<td><code>CurrentSmoothScrollX</code></td>
<td>float</td>
<td>Current smooth scroll position (horizontal)</td>
</tr>
</tbody>
</table>
<h3 id="headers-and-footers">Headers and Footers</h3>
<p>SkiaScroll supports header and footer elements that can behave in special ways:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaScroll HeaderSticky=&quot;True&quot; HeaderBehind=&quot;False&quot;&gt;
    
    &lt;DrawUi:SkiaScroll.Header&gt;
        &lt;DrawUi:SkiaShape Type=&quot;Rectangle&quot; BackgroundColor=&quot;#3498DB&quot; HeightRequest=&quot;80&quot;&gt;
            &lt;DrawUi:SkiaLabel 
                Text=&quot;Sticky Header&quot; 
                TextColor=&quot;White&quot; 
                HorizontalOptions=&quot;Center&quot; 
                VerticalOptions=&quot;Center&quot;
                FontSize=&quot;18&quot; /&gt;
        &lt;/DrawUi:SkiaShape&gt;
    &lt;/DrawUi:SkiaScroll.Header&gt;
    
    &lt;DrawUi:SkiaLayout LayoutType=&quot;Column&quot; Spacing=&quot;10&quot;&gt;
        &lt;!-- Content items --&gt;
    &lt;/DrawUi:SkiaLayout&gt;
    
    &lt;DrawUi:SkiaScroll.Footer&gt;
        &lt;DrawUi:SkiaShape Type=&quot;Rectangle&quot; BackgroundColor=&quot;#2C3E50&quot; HeightRequest=&quot;60&quot;&gt;
            &lt;DrawUi:SkiaLabel 
                Text=&quot;Footer&quot; 
                TextColor=&quot;White&quot; 
                HorizontalOptions=&quot;Center&quot; 
                VerticalOptions=&quot;Center&quot; /&gt;
        &lt;/DrawUi:SkiaShape&gt;
    &lt;/DrawUi:SkiaScroll.Footer&gt;
    
&lt;/DrawUi:SkiaScroll&gt;
</code></pre>
<h3 id="scrolling-to-position">Scrolling to Position</h3>
<pre><code class="lang-csharp">// Scroll to a specific position
myScroll.ScrollToPosition(0, 500); // Scroll to Y = 500

// Scroll with animation
myScroll.ScrollToPosition(0, 500, true); // Animated scroll

// Scroll to an element
myScroll.ScrollToView(targetElement, true); // Animated scroll to element
</code></pre>
<h3 id="virtualization">Virtualization</h3>
<p>For large content sets, you can enable virtualization to improve performance:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaScroll UseVirtual=&quot;True&quot; Orientation=&quot;Vertical&quot;&gt;
    &lt;DrawUi:SkiaLayout 
        LayoutType=&quot;Column&quot; 
        ItemsSource=&quot;{Binding LargeItemCollection}&quot;
        VirtualizationMode=&quot;Enabled&quot;&gt;
        &lt;DrawUi:SkiaLayout.ItemTemplate&gt;
            &lt;DataTemplate&gt;
                &lt;DrawUi:SkiaLabel Text=&quot;{Binding Title}&quot; /&gt;
            &lt;/DataTemplate&gt;
        &lt;/DrawUi:SkiaLayout.ItemTemplate&gt;
    &lt;/DrawUi:SkiaLayout&gt;
&lt;/DrawUi:SkiaScroll&gt;
</code></pre>
<h3 id="pull-to-refresh">Pull-to-Refresh</h3>
<p>SkiaScroll supports pull-to-refresh functionality:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaScroll x:Name=&quot;MyScrollView&quot; Refreshing=&quot;OnRefreshing&quot;&gt;
    
    &lt;DrawUi:SkiaScroll.RefreshIndicator&gt;
        &lt;DrawUi:RefreshIndicator /&gt;
    &lt;/DrawUi:SkiaScroll.RefreshIndicator&gt;
    
    &lt;DrawUi:SkiaLayout LayoutType=&quot;Column&quot;&gt;
        &lt;!-- Content items --&gt;
    &lt;/DrawUi:SkiaLayout&gt;
    
&lt;/DrawUi:SkiaScroll&gt;
</code></pre>
<p>In code-behind:</p>
<pre><code class="lang-csharp">private async void OnRefreshing(object sender, EventArgs e)
{
    // Perform refresh operation
    await LoadDataAsync();
    
    // End refreshing state
    ((SkiaScroll)sender).EndRefresh();
}
</code></pre>
<h2 id="skiascrolllooped">SkiaScrollLooped</h2>
<p>SkiaScrollLooped extends SkiaScroll to provide infinite, looped scrolling capabilities. This is perfect for carousels, banners, and other UI elements that should loop continuously.</p>
<h3 id="basic-usage-1">Basic Usage</h3>
<pre><code class="lang-xml">&lt;DrawUi:SkiaScrollLooped 
    Orientation=&quot;Horizontal&quot;
    WidthRequest=&quot;400&quot;
    HeightRequest=&quot;200&quot;&gt;
    
    &lt;DrawUi:SkiaLayout LayoutType=&quot;Row&quot; Spacing=&quot;10&quot;&gt;
        &lt;DrawUi:SkiaImage Source=&quot;image1.png&quot; WidthRequest=&quot;400&quot; HeightRequest=&quot;200&quot; /&gt;
        &lt;DrawUi:SkiaImage Source=&quot;image2.png&quot; WidthRequest=&quot;400&quot; HeightRequest=&quot;200&quot; /&gt;
        &lt;DrawUi:SkiaImage Source=&quot;image3.png&quot; WidthRequest=&quot;400&quot; HeightRequest=&quot;200&quot; /&gt;
    &lt;/DrawUi:SkiaLayout&gt;
    
&lt;/DrawUi:SkiaScrollLooped&gt;
</code></pre>
<h3 id="key-properties-1">Key Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>IsBanner</code></td>
<td>bool</td>
<td>When true, behaves like a scrolling banner</td>
</tr>
<tr>
<td><code>CycleSpace</code></td>
<td>float</td>
<td>Space between content cycles in pixels</td>
</tr>
</tbody>
</table>
<h3 id="banner-mode">Banner Mode</h3>
<p>In banner mode, there's space between the end of one cycle and the beginning of the next:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaScrollLooped 
    Orientation=&quot;Horizontal&quot;
    IsBanner=&quot;True&quot;
    CycleSpace=&quot;100&quot;
    WidthRequest=&quot;400&quot;
    HeightRequest=&quot;200&quot;&gt;
    
    &lt;!-- Content that will repeat infinitely --&gt;
    &lt;DrawUi:SkiaLabel 
        Text=&quot;Breaking News: DrawnUi.Maui Revolutionizes Cross-Platform UI Development&quot; 
        FontSize=&quot;20&quot;
        TextColor=&quot;Red&quot; /&gt;
    
&lt;/DrawUi:SkiaScrollLooped&gt;
</code></pre>
<h3 id="current-index-tracking">Current Index Tracking</h3>
<p>SkiaScrollLooped can track the current visible index:</p>
<pre><code class="lang-csharp">var scrollLooped = new SkiaScrollLooped
{
    Orientation = ScrollOrientation.Horizontal,
    WidthRequest = 400,
    HeightRequest = 200
};

scrollLooped.CurrentIndexChanged += (s, index) =&gt; {
    Console.WriteLine($&quot;Current visible index: {index}&quot;);
};
</code></pre>
<h2 id="advanced-usage">Advanced Usage</h2>
<h3 id="creating-a-card-carousel">Creating a Card Carousel</h3>
<pre><code class="lang-xml">&lt;DrawUi:SkiaScrollLooped 
    x:Name=&quot;Carousel&quot;
    Orientation=&quot;Horizontal&quot;
    WidthRequest=&quot;400&quot;
    HeightRequest=&quot;300&quot;
    SnapToChildren=&quot;Center&quot;&gt;
    
    &lt;DrawUi:SkiaLayout LayoutType=&quot;Row&quot; Spacing=&quot;20&quot; Padding=&quot;20,0&quot;&gt;
        &lt;!-- Card 1 --&gt;
        &lt;DrawUi:SkiaShape Type=&quot;Rectangle&quot; BackgroundColor=&quot;White&quot; CornerRadius=&quot;16&quot;
                   WidthRequest=&quot;300&quot; HeightRequest=&quot;250&quot;&gt;
            &lt;DrawUi:SkiaShape.Shadows&gt;
                &lt;DrawUi:SkiaShadow Color=&quot;#40000000&quot; BlurRadius=&quot;10&quot; Offset=&quot;0,4&quot; /&gt;
            &lt;/DrawUi:SkiaShape.Shadows&gt;
            
            &lt;DrawUi:SkiaLayout LayoutType=&quot;Column&quot; Padding=&quot;20&quot;&gt;
                &lt;DrawUi:SkiaLabel Text=&quot;Card 1&quot; FontSize=&quot;24&quot; TextColor=&quot;#333333&quot; /&gt;
                &lt;DrawUi:SkiaLabel Text=&quot;Swipe to see more cards&quot; FontSize=&quot;16&quot; TextColor=&quot;#666666&quot; /&gt;
            &lt;/DrawUi:SkiaLayout&gt;
        &lt;/DrawUi:SkiaShape&gt;
        
        &lt;!-- Card 2 --&gt;
        &lt;DrawUi:SkiaShape Type=&quot;Rectangle&quot; BackgroundColor=&quot;White&quot; CornerRadius=&quot;16&quot;
                   WidthRequest=&quot;300&quot; HeightRequest=&quot;250&quot;&gt;
            &lt;DrawUi:SkiaShape.Shadows&gt;
                &lt;DrawUi:SkiaShadow Color=&quot;#40000000&quot; BlurRadius=&quot;10&quot; Offset=&quot;0,4&quot; /&gt;
            &lt;/DrawUi:SkiaShape.Shadows&gt;
            
            &lt;DrawUi:SkiaLayout LayoutType=&quot;Column&quot; Padding=&quot;20&quot;&gt;
                &lt;DrawUi:SkiaLabel Text=&quot;Card 2&quot; FontSize=&quot;24&quot; TextColor=&quot;#333333&quot; /&gt;
                &lt;DrawUi:SkiaLabel Text=&quot;Swipe to see more cards&quot; FontSize=&quot;16&quot; TextColor=&quot;#666666&quot; /&gt;
            &lt;/DrawUi:SkiaLayout&gt;
        &lt;/DrawUi:SkiaShape&gt;
        
        &lt;!-- Card 3 --&gt;
        &lt;DrawUi:SkiaShape Type=&quot;Rectangle&quot; BackgroundColor=&quot;White&quot; CornerRadius=&quot;16&quot;
                   WidthRequest=&quot;300&quot; HeightRequest=&quot;250&quot;&gt;
            &lt;DrawUi:SkiaShape.Shadows&gt;
                &lt;DrawUi:SkiaShadow Color=&quot;#40000000&quot; BlurRadius=&quot;10&quot; Offset=&quot;0,4&quot; /&gt;
            &lt;/DrawUi:SkiaShape.Shadows&gt;
            
            &lt;DrawUi:SkiaLayout LayoutType=&quot;Column&quot; Padding=&quot;20&quot;&gt;
                &lt;DrawUi:SkiaLabel Text=&quot;Card 3&quot; FontSize=&quot;24&quot; TextColor=&quot;#333333&quot; /&gt;
                &lt;DrawUi:SkiaLabel Text=&quot;Swipe to see more cards&quot; FontSize=&quot;16&quot; TextColor=&quot;#666666&quot; /&gt;
            &lt;/DrawUi:SkiaLayout&gt;
        &lt;/DrawUi:SkiaShape&gt;
    &lt;/DrawUi:SkiaLayout&gt;
    
&lt;/DrawUi:SkiaScrollLooped&gt;
</code></pre>
<h3 id="infinite-image-gallery">Infinite Image Gallery</h3>
<pre><code class="lang-xml">&lt;DrawUi:SkiaScrollLooped 
    Orientation=&quot;Horizontal&quot;
    WidthRequest=&quot;400&quot;
    HeightRequest=&quot;400&quot;
    SnapToChildren=&quot;Center&quot;&gt;
    
    &lt;DrawUi:SkiaLayout LayoutType=&quot;Row&quot;&gt;
        &lt;DrawUi:SkiaImage Source=&quot;image1.jpg&quot; WidthRequest=&quot;400&quot; HeightRequest=&quot;400&quot; /&gt;
        &lt;DrawUi:SkiaImage Source=&quot;image2.jpg&quot; WidthRequest=&quot;400&quot; HeightRequest=&quot;400&quot; /&gt;
        &lt;DrawUi:SkiaImage Source=&quot;image3.jpg&quot; WidthRequest=&quot;400&quot; HeightRequest=&quot;400&quot; /&gt;
        &lt;DrawUi:SkiaImage Source=&quot;image4.jpg&quot; WidthRequest=&quot;400&quot; HeightRequest=&quot;400&quot; /&gt;
    &lt;/DrawUi:SkiaLayout&gt;
    
&lt;/DrawUi:SkiaScrollLooped&gt;
</code></pre>
<h3 id="building-a-feed-with-pull-to-refresh">Building a Feed with Pull-to-Refresh</h3>
<pre><code class="lang-xml">&lt;DrawUi:SkiaScroll x:Name=&quot;FeedScroll&quot; Refreshing=&quot;OnRefreshingFeed&quot;&gt;
    
    &lt;DrawUi:SkiaScroll.RefreshIndicator&gt;
        &lt;DrawUi:RefreshIndicator /&gt;
    &lt;/DrawUi:SkiaScroll.RefreshIndicator&gt;
    
    &lt;DrawUi:SkiaLayout 
        LayoutType=&quot;Column&quot; 
        Spacing=&quot;12&quot; 
        Padding=&quot;16&quot;
        ItemsSource=&quot;{Binding FeedItems}&quot;&gt;
        &lt;DrawUi:SkiaLayout.ItemTemplate&gt;
            &lt;DataTemplate&gt;
                &lt;DrawUi:SkiaShape Type=&quot;Rectangle&quot; BackgroundColor=&quot;White&quot; CornerRadius=&quot;8&quot;&gt;
                    &lt;DrawUi:SkiaShape.Shadows&gt;
                        &lt;DrawUi:SkiaShadow Color=&quot;#20000000&quot; BlurRadius=&quot;4&quot; Offset=&quot;0,2&quot; /&gt;
                    &lt;/DrawUi:SkiaShape.Shadows&gt;
                    
                    &lt;DrawUi:SkiaLayout LayoutType=&quot;Column&quot; Padding=&quot;16&quot;&gt;
                        &lt;DrawUi:SkiaLabel Text=&quot;{Binding Title}&quot; FontSize=&quot;18&quot; TextColor=&quot;#333333&quot; /&gt;
                        &lt;DrawUi:SkiaLabel Text=&quot;{Binding Description}&quot; FontSize=&quot;14&quot; TextColor=&quot;#666666&quot; /&gt;
                    &lt;/DrawUi:SkiaLayout&gt;
                &lt;/DrawUi:SkiaShape&gt;
            &lt;/DataTemplate&gt;
        &lt;/DrawUi:SkiaLayout.ItemTemplate&gt;
    &lt;/DrawUi:SkiaLayout&gt;
    
&lt;/DrawUi:SkiaScroll&gt;
</code></pre>
<h2 id="performance-considerations">Performance Considerations</h2>
<h3 id="virtualization-1">Virtualization</h3>
<p>For optimal performance with large datasets:</p>
<ul>
<li>Enable <code>UseVirtual=&quot;True&quot;</code> on SkiaScroll</li>
<li>Use <code>VirtualizationMode=&quot;Enabled&quot;</code> on inner SkiaLayout</li>
<li>Consider <code>RecyclingTemplate=&quot;Enabled&quot;</code> for template reuse</li>
</ul>
<h3 id="content-size">Content Size</h3>
<p>When working with infinite scrolling:</p>
<ul>
<li>Monitor memory usage, especially with large images</li>
<li>Use <code>Cache=&quot;Operations&quot;</code> for content that changes frequently</li>
<li>For better performance with large collections, consider using data virtualization alongside UI virtualization</li>
</ul>
<h3 id="gestures">Gestures</h3>
<p>If scroll gesture handling conflicts with other gesture recognizers:</p>
<ul>
<li>Adjust <code>ScrollVelocityThreshold</code> to control sensitivity</li>
<li>For nested scrolling scenarios, ensure proper gesture propagation</li>
<li>Consider using <code>TouchScrollFriendly</code> on inner components that need to receive touch events</li>
</ul>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/scroll.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../../styles/docfx.js"></script>
    <script type="text/javascript" src="../../styles/main.js"></script>
  </body>
</html>
