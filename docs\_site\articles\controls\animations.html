<!DOCTYPE html>
<!--[if IE]><![endif]-->
<html>

  <head>
    <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
      <title>Animation Controls | DrawnUi Documentation </title>
      <meta name="viewport" content="width=device-width">
      <meta name="title" content="Animation Controls | DrawnUi Documentation ">
    
    
      <link rel="shortcut icon" href="../../images/favicon.ico">
      <link rel="stylesheet" href="../../styles/docfx.vendor.min.css">
      <link rel="stylesheet" href="../../styles/docfx.css">
      <link rel="stylesheet" href="../../styles/main.css">
      <meta property="docfx:navrel" content="../../toc.html">
      <meta property="docfx:tocrel" content="toc.html">
    
    <meta property="docfx:rel" content="../../">
    
  </head>
  <body data-spy="scroll" data-target="#affix" data-offset="120">
    <div id="wrapper">
      <header>

        <nav id="autocollapse" class="navbar navbar-inverse ng-scope" role="navigation">
          <div class="container">
            <div class="navbar-header">
              <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#navbar">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
              </button>

              <a class="navbar-brand" href="../../index.html">
                <img id="logo" class="svg" src="../../images/logo.png" alt="">
              </a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
              <form class="navbar-form navbar-right" role="search" id="search">
                <div class="form-group">
                  <input type="text" class="form-control" id="search-query" placeholder="Search" autocomplete="off">
                </div>
              </form>
            </div>
          </div>
        </nav>

        <div class="subnav navbar navbar-default">
          <div class="container hide-when-search" id="breadcrumb">
            <ul class="breadcrumb">
              <li></li>
            </ul>
          </div>
        </div>
      </header>
      <div class="container body-content">

        <div id="search-results">
          <div class="search-list">Search Results for <span></span></div>
          <div class="sr-items">
            <p><i class="glyphicon glyphicon-refresh index-loading"></i></p>
          </div>
          <ul id="pagination" data-first=First data-prev=Previous data-next=Next data-last=Last></ul>
        </div>
      </div>
      <div role="main" class="container body-content hide-when-search">

        <div class="sidenav hide-when-search">
          <a class="btn toc-toggle collapse" data-toggle="collapse" href="#sidetoggle" aria-expanded="false" aria-controls="sidetoggle">Show / Hide Table of Contents</a>
          <div class="sidetoggle collapse" id="sidetoggle">
            <div id="sidetoc"></div>
          </div>
        </div>
        <div class="article row grid-right">
          <div class="col-md-10">
            <article class="content wrap" id="_content" data-uid="">
<h1 id="animation-controls">Animation Controls</h1>

<p>DrawnUi.Maui provides powerful controls for displaying animations directly on the canvas with high performance. This article covers the animation controls available in the framework.</p>
<h2 id="animation-basics">Animation Basics</h2>
<p>All animation controls in DrawnUi.Maui share common functionality through the <code>AnimatedFramesRenderer</code> base class. This provides consistent playback control and event handling across different animation types.</p>
<p>Common properties and methods include:</p>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>AutoPlay</code></td>
<td>bool</td>
<td>Automatically start animation when loaded</td>
</tr>
<tr>
<td><code>IsPlaying</code></td>
<td>bool</td>
<td>Indicates if animation is currently playing</td>
</tr>
<tr>
<td><code>Repeat</code></td>
<td>int</td>
<td>Number of times to repeat (-1 for infinite looping)</td>
</tr>
<tr>
<td><code>SpeedRatio</code></td>
<td>double</td>
<td>Animation playback speed multiplier</td>
</tr>
<tr>
<td><code>DefaultFrame</code></td>
<td>int</td>
<td>Frame to display when not playing</td>
</tr>
</tbody>
</table>
<p>Common methods:</p>
<ul>
<li><code>Start()</code> - Begin or resume the animation</li>
<li><code>Stop()</code> - Pause the animation</li>
<li><code>Seek(frame)</code> - Jump to a specific frame</li>
</ul>
<p>Common events:</p>
<ul>
<li><code>Started</code> - Fires when animation begins</li>
<li><code>Finished</code> - Fires when animation completes</li>
</ul>
<h2 id="skiagif">SkiaGif</h2>
<p>SkiaGif is a control for displaying animated GIF images with precise frame timing and control.</p>
<h3 id="basic-usage">Basic Usage</h3>
<pre><code class="lang-xml">&lt;DrawUi:SkiaGif
    Source=&quot;animated.gif&quot;
    WidthRequest=&quot;200&quot;
    HeightRequest=&quot;200&quot;
    AutoPlay=&quot;True&quot;
    Repeat=&quot;-1&quot; /&gt;
</code></pre>
<h3 id="key-properties">Key Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Source</code></td>
<td>string</td>
<td>Path or URL to the GIF file</td>
</tr>
<tr>
<td><code>Animation</code></td>
<td>GifAnimation</td>
<td>Internal animation data (automatically created)</td>
</tr>
</tbody>
</table>
<h3 id="loading-sources">Loading Sources</h3>
<p>SkiaGif can load animations from various sources:</p>
<pre><code class="lang-csharp">// From app resources
myGif.Source = &quot;embedded_resource.gif&quot;;

// From file system
myGif.Source = &quot;file:///path/to/animation.gif&quot;;

// From URL
myGif.Source = &quot;https://example.com/animation.gif&quot;;
</code></pre>
<h3 id="controlling-playback">Controlling Playback</h3>
<pre><code class="lang-csharp">// Start the animation
myGif.Start();

// Stop at current frame
myGif.Stop();

// Jump to specific frame
myGif.Seek(5);

// Get total frames
int total = myGif.Animation?.TotalFrames ?? 0;
</code></pre>
<h2 id="skialottie">SkiaLottie</h2>
<p>SkiaLottie is a control for displaying <a href="https://airbnb.io/lottie/">Lottie animations</a>, which are vector-based animations exported from Adobe After Effects. It provides smooth, resolution-independent animations with additional customization options.</p>
<h3 id="basic-usage-1">Basic Usage</h3>
<pre><code class="lang-xml">&lt;DrawUi:SkiaLottie
    Source=&quot;animation.json&quot;
    WidthRequest=&quot;200&quot;
    HeightRequest=&quot;200&quot;
    AutoPlay=&quot;True&quot;
    Repeat=&quot;-1&quot; /&gt;
</code></pre>
<h3 id="toggle-state-support">Toggle State Support</h3>
<p>SkiaLottie includes special support for toggle/switch animations with the <code>IsOn</code> property:</p>
<pre><code class="lang-xml">&lt;DrawUi:SkiaLottie
    Source=&quot;toggle_animation.json&quot;
    IsOn=&quot;{Binding IsToggled}&quot;
    DefaultFrame=&quot;0&quot;
    DefaultFrameWhenOn=&quot;30&quot;
    SpeedRatio=&quot;1.5&quot;
    AutoPlay=&quot;True&quot;
    Repeat=&quot;0&quot; /&gt;
</code></pre>
<p>This is perfect for animated toggles, checkboxes, or any other animation with distinct on/off states.</p>
<h3 id="key-properties-1">Key Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Source</code></td>
<td>string</td>
<td>Path or URL to the Lottie JSON file</td>
</tr>
<tr>
<td><code>ColorTint</code></td>
<td>Color</td>
<td>Color tint applied to the entire animation</td>
</tr>
<tr>
<td><code>Colors</code></td>
<td>IList<color></color></td>
<td>Collection of replacement colors</td>
</tr>
<tr>
<td><code>IsOn</code></td>
<td>bool</td>
<td>Toggle state property</td>
</tr>
<tr>
<td><code>DefaultFrameWhenOn</code></td>
<td>int</td>
<td>Frame to display when IsOn = true</td>
</tr>
<tr>
<td><code>ApplyIsOnWhenNotPlaying</code></td>
<td>bool</td>
<td>Whether to apply IsOn state when not playing</td>
</tr>
</tbody>
</table>
<h3 id="customizing-colors">Customizing Colors</h3>
<p>One of the powerful features of SkiaLottie is color customization:</p>
<pre><code class="lang-xml">&lt;!-- Apply a global tint --&gt;
&lt;DrawUi:SkiaLottie
    Source=&quot;animation.json&quot;
    ColorTint=&quot;Red&quot; /&gt;
</code></pre>
<p>For more granular control, you can replace multiple colors:</p>
<pre><code class="lang-csharp">// Replace specific colors in the animation
myLottie.Colors.Add(Colors.Blue);
myLottie.Colors.Add(Colors.Green);

// Apply changes
myLottie.ReloadSource();
</code></pre>
<h2 id="skiasprite">SkiaSprite</h2>
<p>SkiaSprite is a high-performance control for displaying and animating sprite sheets. It loads sprite sheets (a single image containing multiple animation frames arranged in a grid) and renders individual frames with precise timing for smooth animations.</p>
<h3 id="basic-usage-2">Basic Usage</h3>
<pre><code class="lang-xml">&lt;DrawUi:SkiaSprite
    Source=&quot;sprites/explosion.png&quot;
    Columns=&quot;8&quot;
    Rows=&quot;4&quot;
    FramesPerSecond=&quot;24&quot;
    AutoPlay=&quot;True&quot;
    Repeat=&quot;-1&quot;
    WidthRequest=&quot;128&quot;
    HeightRequest=&quot;128&quot; /&gt;
</code></pre>
<h3 id="key-properties-2">Key Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Source</code></td>
<td>string</td>
<td>Path or URL of the sprite sheet image</td>
</tr>
<tr>
<td><code>Columns</code></td>
<td>int</td>
<td>Number of columns in the sprite sheet grid</td>
</tr>
<tr>
<td><code>Rows</code></td>
<td>int</td>
<td>Number of rows in the sprite sheet grid</td>
</tr>
<tr>
<td><code>FramesPerSecond</code></td>
<td>int</td>
<td>Animation speed in frames per second (default: 24)</td>
</tr>
<tr>
<td><code>MaxFrames</code></td>
<td>int</td>
<td>Maximum number of frames to use (0 means use all)</td>
</tr>
<tr>
<td><code>CurrentFrame</code></td>
<td>int</td>
<td>Current frame being displayed (0-based index)</td>
</tr>
<tr>
<td><code>FrameSequence</code></td>
<td>int[]</td>
<td>Custom sequence of frames to play</td>
</tr>
<tr>
<td><code>AnimationName</code></td>
<td>string</td>
<td>Name of a predefined animation sequence</td>
</tr>
</tbody>
</table>
<h3 id="sprite-sheet-structure">Sprite Sheet Structure</h3>
<p>A sprite sheet is a single image containing multiple frames arranged in a grid:</p>
<pre><code>+---+---+---+---+
| 0 | 1 | 2 | 3 |
+---+---+---+---+
| 4 | 5 | 6 | 7 |
+---+---+---+---+
| 8 | 9 | 10| 11|
+---+---+---+---+
</code></pre>
<p>The <code>Columns</code> and <code>Rows</code> properties define the grid structure:</p>
<ul>
<li>In the example above, set <code>Columns=&quot;4&quot;</code> and <code>Rows=&quot;3&quot;</code></li>
<li>Frames are numbered left-to-right, top-to-bottom (0 to 11)</li>
<li>Each frame must have the same dimensions</li>
</ul>
<h3 id="frame-sequences-and-reusing-spritesheets">Frame Sequences and Reusing Spritesheets</h3>
<p>One of the key features of SkiaSprite is the ability to create multiple animations from a single spritesheet by defining frame sequences:</p>
<pre><code class="lang-csharp">// Register named animations for a character spritesheet
SkiaSprite.CreateAnimationSequence(&quot;Idle&quot;, new[] { 0, 1, 2, 1 });
SkiaSprite.CreateAnimationSequence(&quot;Walk&quot;, new[] { 3, 4, 5, 6, 7, 8 });
SkiaSprite.CreateAnimationSequence(&quot;Jump&quot;, new[] { 9, 10, 11 });
</code></pre>
<p>Then in XAML just reference by name:</p>
<pre><code class="lang-xml">&lt;!-- Multiple sprites sharing the same spritesheet with different animations --&gt;
&lt;DrawUi:SkiaSprite Source=&quot;character.png&quot; AnimationName=&quot;Walk&quot; /&gt;
&lt;DrawUi:SkiaSprite Source=&quot;character.png&quot; AnimationName=&quot;Jump&quot; /&gt;
</code></pre>
<p>Or use a direct frame sequence:</p>
<pre><code class="lang-csharp">// Define a specific frame sequence
mySprite.FrameSequence = new[] { 3, 4, 5, 4, 3 }; // Play frames in this exact order
</code></pre>
<h3 id="spritesheet-caching">Spritesheet Caching</h3>
<p>SkiaSprite includes an intelligent caching system to avoid reloading the same spritesheets multiple times:</p>
<pre><code class="lang-csharp">// Clear the entire spritesheet cache
SkiaSprite.ClearCache();

// Remove a specific spritesheet from cache
SkiaSprite.RemoveFromCache(&quot;character.png&quot;);
</code></pre>
<h2 id="performance-considerations">Performance Considerations</h2>
<h3 id="skiagif-1">SkiaGif</h3>
<ul>
<li>GIFs can consume significant memory, especially large ones</li>
<li>For large animations, verify memory usage</li>
</ul>
<h3 id="skialottie-1">SkiaLottie</h3>
<ul>
<li>Vector-based animations are more memory efficient than GIFs</li>
<li>Complex Lottie animations may be CPU-intensive</li>
<li>Use <code>ColorTint</code> for simple color changes rather than individual color replacements when possible</li>
</ul>
<h3 id="skiasprite-1">SkiaSprite</h3>
<ul>
<li>Spritesheets are cached automatically to avoid redundant loading</li>
<li>For large or numerous sprite sheets, consider monitoring memory usage</li>
<li>Use <code>ClearCache()</code> or <code>RemoveFromCache()</code> when spritesheets are no longer needed</li>
<li>For complex animations, use frame sequences to avoid redundant frames</li>
</ul>
<h2 id="examples">Examples</h2>
<h3 id="loading-animation-with-lottie">Loading Animation with Lottie</h3>
<pre><code class="lang-xml">&lt;DrawUi:SkiaLottie
    Source=&quot;loading_spinner.json&quot;
    WidthRequest=&quot;48&quot;
    HeightRequest=&quot;48&quot;
    AutoPlay=&quot;True&quot;
    Repeat=&quot;-1&quot; /&gt;
</code></pre>
<h3 id="animated-button-with-sprite-sheet">Animated Button with Sprite Sheet</h3>
<pre><code class="lang-xml">&lt;DrawUi:SkiaButton
    WidthRequest=&quot;200&quot;
    HeightRequest=&quot;60&quot;
    BackgroundColor=&quot;Transparent&quot;&gt;
    
    &lt;DrawUi:SkiaSprite
        x:Name=&quot;buttonAnimation&quot;
        Source=&quot;button_animation.png&quot;
        Columns=&quot;5&quot;
        Rows=&quot;1&quot;
        FramesPerSecond=&quot;30&quot;
        AutoPlay=&quot;False&quot;
        DefaultFrame=&quot;0&quot; /&gt;
    
    &lt;DrawUi:SkiaLabel
        Text=&quot;Animated Button&quot;
        TextColor=&quot;White&quot;
        FontSize=&quot;16&quot;
        HorizontalOptions=&quot;Center&quot;
        VerticalOptions=&quot;Center&quot; /&gt;
&lt;/DrawUi:SkiaButton&gt;
</code></pre>
<p>In code-behind:</p>
<pre><code class="lang-csharp">MyButton.Pressed += (s, e) =&gt; {
    buttonAnimation.Stop();
    buttonAnimation.CurrentFrame = 0;
    buttonAnimation.Start();
};
</code></pre>
<h3 id="game-character-animation">Game Character Animation</h3>
<pre><code class="lang-xml">&lt;DrawUi:SkiaLayout
    WidthRequest=&quot;200&quot;
    HeightRequest=&quot;200&quot;&gt;
    
    &lt;DrawUi:SkiaSprite
        x:Name=&quot;CharacterAnimation&quot;
        Source=&quot;character_sprites.png&quot;
        Columns=&quot;8&quot;
        Rows=&quot;4&quot;
        FramesPerSecond=&quot;12&quot;
        AnimationName=&quot;Walk&quot;
        AutoPlay=&quot;True&quot;
        WidthRequest=&quot;128&quot;
        HeightRequest=&quot;128&quot;
        HorizontalOptions=&quot;Center&quot;
        VerticalOptions=&quot;Center&quot; /&gt;
        
&lt;/DrawUi:SkiaLayout&gt;
</code></pre>
<p>In code-behind:</p>
<pre><code class="lang-csharp">// Setup animation sequences
void InitializeAnimations()
{
    SkiaSprite.CreateAnimationSequence(&quot;Idle&quot;, new[] { 0, 1, 2, 1 });
    SkiaSprite.CreateAnimationSequence(&quot;Walk&quot;, new[] { 8, 9, 10, 11, 12, 13, 14, 15 });
    SkiaSprite.CreateAnimationSequence(&quot;Jump&quot;, new[] { 16, 17, 18, 19, 20, 21 });
    SkiaSprite.CreateAnimationSequence(&quot;Attack&quot;, new[] { 24, 25, 26, 27, 28, 29, 30 });
}

// Change animation based on game state
void UpdateCharacterState(PlayerState state)
{
    CharacterAnimation.Stop();
    
    switch (state)
    {
        case PlayerState.Idle:
            CharacterAnimation.AnimationName = &quot;Idle&quot;;
            break;
        case PlayerState.Walking:
            CharacterAnimation.AnimationName = &quot;Walk&quot;;
            break;
        case PlayerState.Jumping:
            CharacterAnimation.AnimationName = &quot;Jump&quot;;
            break;
        case PlayerState.Attacking:
            CharacterAnimation.AnimationName = &quot;Attack&quot;;
            break;
    }
    
    CharacterAnimation.Start();
}
</code></pre>
<h3 id="gif-avatar">GIF Avatar</h3>
<pre><code class="lang-xml">&lt;draw:SkiaShape
    Type=&quot;Circle&quot;
    WidthRequest=&quot;100&quot;
    LockRatio=&quot;1&quot;&gt;
    
    &lt;draw:SkiaGif
        Source=&quot;avatar.gif&quot;
        WidthRequest=&quot;100&quot;
        LockRatio=&quot;1&quot;
        AutoPlay=&quot;True&quot;
        Repeat=&quot;-1&quot; /&gt;
&lt;/draw:SkiaShape&gt;
</code></pre>

</article>
          </div>

          <div class="hidden-sm col-md-2" role="complementary">
            <div class="sideaffix">
              <div class="contribution">
                <ul class="nav">
                  <li>
                    <a href="https://github.com/taublast/DrawnUi/blob/master/docs/articles/controls/animations.md/#L1" class="contribution-link">Edit this page</a>
                  </li>
                </ul>
              </div>
              <nav class="bs-docs-sidebar hidden-print hidden-xs hidden-sm affix" id="affix">
                <h5>In this article</h5>
                <div></div>
              </nav>
            </div>
          </div>
        </div>
      </div>

      <footer>
        <div class="grad-bottom"></div>
        <div class="footer">
          <div class="container">
            <span class="pull-right">
              <a href="#top">Back to top</a>
            </span>
      
      <span>Generated by <strong>DocFX</strong></span>
          </div>
        </div>
      </footer>
    </div>

    <script type="text/javascript" src="../../styles/docfx.vendor.min.js"></script>
    <script type="text/javascript" src="../../styles/docfx.js"></script>
    <script type="text/javascript" src="../../styles/main.js"></script>
  </body>
</html>
